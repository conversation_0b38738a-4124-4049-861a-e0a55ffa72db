# نظام المحاسبة الشامل

نظام محاسبي متكامل يعمل على سطح المكتب بدون الحاجة للإنترنت، مصمم خصيصاً للشركات والمصانع الصغيرة والمتوسطة.

## المميزات

### الوحدات الرئيسية:
1. **كشف الحساب** - إدارة وعرض كشوفات الحسابات المالية
2. **مشتريات المخزن** - تتبع وإدارة مشتريات المواد والبضائع
3. **مصروفات النقل** - تسجيل ومتابعة تكاليف النقل والشحن
4. **مصروفات المعيشة** - إدارة مصروفات المعيشة والإقامة
5. **مصروفات الطلاء** - تتبع تكاليف مواد وعمليات الطلاء
6. **مصروفات المصنع** - إدارة جميع المصروفات التشغيلية
7. **مسحوبات الموظفين** - تتبع السلف والمسحوبات المالية
8. **إيداعات الخزينة** - إدارة الإيداعات والمقبوضات النقدية

### المميزات التقنية:
- ✅ يعمل بدون إنترنت
- ✅ واجهة عربية بالكامل
- ✅ قاعدة بيانات محلية (SQLite)
- ✅ تقارير شهرية شاملة
- ✅ نسخ احتياطي للبيانات
- ✅ واجهة مستخدم سهلة وحديثة

## التقنيات المستخدمة

- **Frontend**: React.js مع Electron
- **Backend**: Node.js
- **Database**: SQLite
- **UI**: CSS مخصص مع تصميم عربي

## متطلبات التشغيل

- Windows 10 أو أحدث
- Node.js 16 أو أحدث
- 4 GB RAM على الأقل
- 500 MB مساحة فارغة

## طريقة التثبيت والتشغيل

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
https://nodejs.org/

### 2. تحميل المشروع
```bash
git clone [repository-url]
cd accounting-system
```

### 3. تثبيت المكتبات
```bash
npm install
```

### 4. تشغيل التطبيق في وضع التطوير
```bash
npm run electron-dev
```

### 5. بناء التطبيق للإنتاج
```bash
npm run electron-pack
```

## كيفية الاستخدام

### البدء السريع:
1. افتح التطبيق
2. ستظهر لوحة التحكم الرئيسية
3. استخدم الشريط الجانبي للتنقل بين الوحدات
4. ابدأ بإدخال البيانات في كل وحدة حسب الحاجة

### إدخال البيانات:
- انقر على زر "إضافة" في أي وحدة
- املأ النموذج بالبيانات المطلوبة
- انقر "حفظ" لحفظ البيانات

### عرض التقارير:
- اذهب إلى قسم "التقارير"
- اختر الشهر المطلوب
- اختر نوع التقرير
- انقر "تصدير PDF" لحفظ التقرير

## هيكل المشروع

```
accounting-system/
├── public/
│   ├── electron.js          # ملف Electron الرئيسي
│   ├── database.js          # إعدادات قاعدة البيانات
│   └── index.html           # ملف HTML الرئيسي
├── src/
│   ├── components/          # مكونات React
│   │   ├── Dashboard.js     # لوحة التحكم
│   │   ├── Sidebar.js       # الشريط الجانبي
│   │   ├── Header.js        # الهيدر
│   │   └── [Other Components]
│   ├── App.js              # المكون الرئيسي
│   ├── App.css             # الأنماط الرئيسية
│   └── index.js            # نقطة البداية
├── package.json            # إعدادات المشروع
└── README.md              # هذا الملف
```

## النسخ الاحتياطي

يتم حفظ قاعدة البيانات في:
```
%USERPROFILE%/AppData/Roaming/accounting-system/accounting.db
```

يُنصح بعمل نسخة احتياطية من هذا الملف بانتظام.

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع الوثائق أولاً
- تأكد من تحديث Node.js
- تحقق من رسائل الخطأ في وحدة التحكم

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## المطورون

تم تطوير هذا النظام بواسطة فريق التطوير المحلي.

---

**ملاحظة**: هذا النظام في مرحلة التطوير الأولى. المميزات الإضافية ستتم إضافتها في الإصدارات القادمة.
