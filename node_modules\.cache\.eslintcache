[{"C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Header.js": "3", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Sidebar.js": "4", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\TransportExpenses.js": "5", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Dashboard.js": "6", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\FactoryExpenses.js": "7", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\EmployeeWithdrawals.js": "8", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\PaintExpenses.js": "9", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\TreasuryDeposits.js": "10", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\AccountStatements.js": "11", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Reports.js": "12", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\WarehousePurchases.js": "13", "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\LivingExpenses.js": "14"}, {"size": 232, "mtime": *************, "results": "15", "hashOfConfig": "16"}, {"size": 1883, "mtime": *************, "results": "17", "hashOfConfig": "16"}, {"size": 1558, "mtime": *************, "results": "18", "hashOfConfig": "16"}, {"size": 1813, "mtime": *************, "results": "19", "hashOfConfig": "16"}, {"size": 3069, "mtime": *************, "results": "20", "hashOfConfig": "16"}, {"size": 5689, "mtime": *************, "results": "21", "hashOfConfig": "16"}, {"size": 2325, "mtime": *************, "results": "22", "hashOfConfig": "16"}, {"size": 3233, "mtime": *************, "results": "23", "hashOfConfig": "16"}, {"size": 2413, "mtime": *************, "results": "24", "hashOfConfig": "16"}, {"size": 3030, "mtime": 1748906201485, "results": "25", "hashOfConfig": "16"}, {"size": 9088, "mtime": 1748906077686, "results": "26", "hashOfConfig": "16"}, {"size": 6869, "mtime": 1748906225340, "results": "27", "hashOfConfig": "16"}, {"size": 9141, "mtime": 1748906114527, "results": "28", "hashOfConfig": "16"}, {"size": 2214, "mtime": 1748906144237, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gjt08o", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Sidebar.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\TransportExpenses.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\FactoryExpenses.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\EmployeeWithdrawals.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\PaintExpenses.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\TreasuryDeposits.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\AccountStatements.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\Reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\WarehousePurchases.js", [], [], "C:\\Users\\<USER>\\Desktop\\منضومة خفيفة\\src\\components\\LivingExpenses.js", [], []]