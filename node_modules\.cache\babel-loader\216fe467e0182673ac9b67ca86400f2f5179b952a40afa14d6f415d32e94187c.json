{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));\n  const [reportType, setReportType] = useState('شامل');\n  const reportData = {\n    income: 150000,\n    expenses: {\n      warehouse: 25000,\n      transport: 8500,\n      living: 5200,\n      paint: 12000,\n      factory: 18500,\n      employees: 15000\n    },\n    deposits: 67000,\n    withdrawals: 12000\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const getTotalExpenses = () => {\n    return Object.values(reportData.expenses).reduce((sum, expense) => sum + expense, 0);\n  };\n  const getNetProfit = () => {\n    return reportData.income - getTotalExpenses();\n  };\n  const generatePDF = () => {\n    alert('سيتم إضافة وظيفة تصدير PDF قريباً');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reports\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedMonth,\n            onChange: e => setSelectedMonth(e.target.value),\n            className: \"form-input\",\n            style: {\n              width: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2024-01\",\n              children: \"\\u064A\\u0646\\u0627\\u064A\\u0631 2024\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2023-12\",\n              children: \"\\u062F\\u064A\\u0633\\u0645\\u0628\\u0631 2023\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2023-11\",\n              children: \"\\u0646\\u0648\\u0641\\u0645\\u0628\\u0631 2023\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: reportType,\n            onChange: e => setReportType(e.target.value),\n            className: \"form-input\",\n            style: {\n              width: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\\u0634\\u0627\\u0645\\u0644\",\n              children: \"\\u062A\\u0642\\u0631\\u064A\\u0631 \\u0634\\u0627\\u0645\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\",\n              children: \"\\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A \\u0641\\u0642\\u0637\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\",\n              children: \"\\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0641\\u0642\\u0637\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: generatePDF,\n            children: \"\\uD83D\\uDCC4 \\u062A\\u0635\\u062F\\u064A\\u0631 PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        style: {\n          marginBottom: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            style: {\n              color: '#28a745'\n            },\n            children: formatCurrency(reportData.income)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u0631\\u0627\\u062F\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            style: {\n              color: '#dc3545'\n            },\n            children: formatCurrency(getTotalExpenses())\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-value\",\n            style: {\n              color: getNetProfit() >= 0 ? '#007bff' : '#dc3545'\n            },\n            children: formatCurrency(getNetProfit())\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u0631\\u0628\\u062D/\\u0627\\u0644\\u062E\\u0633\\u0627\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            margin: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '15px'\n            },\n            children: \"\\u062A\\u0641\\u0635\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-container\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"\\u0627\\u0644\\u0646\\u0633\\u0628\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.warehouse)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.warehouse / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0646\\u0642\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.transport)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.transport / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u064A\\u0634\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.living)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.living / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0637\\u0644\\u0627\\u0621\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.paint)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.paint / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.factory)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.factory / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: \"\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0638\\u0641\\u064A\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(reportData.expenses.employees)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [(reportData.expenses.employees / getTotalExpenses() * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            margin: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '15px'\n            },\n            children: \"\\u062D\\u0631\\u0643\\u0629 \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '15px',\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0625\\u064A\\u062F\\u0627\\u0639\\u0627\\u062A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                style: {\n                  color: '#28a745'\n                },\n                children: formatCurrency(reportData.deposits)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '15px',\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0633\\u062D\\u0648\\u0628\\u0627\\u062A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                style: {\n                  color: '#dc3545'\n                },\n                children: formatCurrency(reportData.withdrawals)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                fontSize: '18px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                style: {\n                  color: '#007bff'\n                },\n                children: formatCurrency(reportData.deposits - reportData.withdrawals)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"FkF9Z4PDBCtoM4KdUh+be7JTw1w=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Reports", "_s", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "Date", "toISOString", "slice", "reportType", "setReportType", "reportData", "income", "expenses", "warehouse", "transport", "living", "paint", "factory", "employees", "deposits", "withdrawals", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getTotalExpenses", "Object", "values", "reduce", "sum", "expense", "getNetProfit", "generatePDF", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gap", "value", "onChange", "e", "target", "width", "onClick", "marginBottom", "color", "gridTemplateColumns", "margin", "toFixed", "padding", "justifyContent", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Reports.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Reports = () => {\n  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));\n  const [reportType, setReportType] = useState('شامل');\n\n  const reportData = {\n    income: 150000,\n    expenses: {\n      warehouse: 25000,\n      transport: 8500,\n      living: 5200,\n      paint: 12000,\n      factory: 18500,\n      employees: 15000\n    },\n    deposits: 67000,\n    withdrawals: 12000\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getTotalExpenses = () => {\n    return Object.values(reportData.expenses).reduce((sum, expense) => sum + expense, 0);\n  };\n\n  const getNetProfit = () => {\n    return reportData.income - getTotalExpenses();\n  };\n\n  const generatePDF = () => {\n    alert('سيتم إضافة وظيفة تصدير PDF قريباً');\n  };\n\n  return (\n    <div className=\"reports\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">التقارير المالية</h3>\n          <div style={{ display: 'flex', gap: '10px' }}>\n            <select \n              value={selectedMonth}\n              onChange={(e) => setSelectedMonth(e.target.value)}\n              className=\"form-input\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"2024-01\">يناير 2024</option>\n              <option value=\"2023-12\">ديسمبر 2023</option>\n              <option value=\"2023-11\">نوفمبر 2023</option>\n            </select>\n            \n            <select \n              value={reportType}\n              onChange={(e) => setReportType(e.target.value)}\n              className=\"form-input\"\n              style={{ width: 'auto' }}\n            >\n              <option value=\"شامل\">تقرير شامل</option>\n              <option value=\"إيرادات\">الإيرادات فقط</option>\n              <option value=\"مصروفات\">المصروفات فقط</option>\n            </select>\n            \n            <button className=\"btn btn-primary\" onClick={generatePDF}>\n              📄 تصدير PDF\n            </button>\n          </div>\n        </div>\n\n        {/* ملخص مالي */}\n        <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: '#28a745' }}>\n              {formatCurrency(reportData.income)}\n            </div>\n            <div className=\"stat-label\">إجمالي الإيرادات</div>\n          </div>\n          \n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n              {formatCurrency(getTotalExpenses())}\n            </div>\n            <div className=\"stat-label\">إجمالي المصروفات</div>\n          </div>\n          \n          <div className=\"stat-card\">\n            <div className=\"stat-value\" style={{ color: getNetProfit() >= 0 ? '#007bff' : '#dc3545' }}>\n              {formatCurrency(getNetProfit())}\n            </div>\n            <div className=\"stat-label\">صافي الربح/الخسارة</div>\n          </div>\n        </div>\n\n        {/* تفصيل المصروفات */}\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>\n          <div className=\"card\" style={{ margin: 0 }}>\n            <h4 style={{ marginBottom: '15px' }}>تفصيل المصروفات</h4>\n            <div className=\"table-container\">\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    <th>نوع المصروف</th>\n                    <th>المبلغ</th>\n                    <th>النسبة</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td>مشتريات المخزن</td>\n                    <td>{formatCurrency(reportData.expenses.warehouse)}</td>\n                    <td>{((reportData.expenses.warehouse / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات النقل</td>\n                    <td>{formatCurrency(reportData.expenses.transport)}</td>\n                    <td>{((reportData.expenses.transport / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات المعيشة</td>\n                    <td>{formatCurrency(reportData.expenses.living)}</td>\n                    <td>{((reportData.expenses.living / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات الطلاء</td>\n                    <td>{formatCurrency(reportData.expenses.paint)}</td>\n                    <td>{((reportData.expenses.paint / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مصروفات المصنع</td>\n                    <td>{formatCurrency(reportData.expenses.factory)}</td>\n                    <td>{((reportData.expenses.factory / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                  <tr>\n                    <td>مسحوبات الموظفين</td>\n                    <td>{formatCurrency(reportData.expenses.employees)}</td>\n                    <td>{((reportData.expenses.employees / getTotalExpenses()) * 100).toFixed(1)}%</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          <div className=\"card\" style={{ margin: 0 }}>\n            <h4 style={{ marginBottom: '15px' }}>حركة الخزينة</h4>\n            <div style={{ padding: '20px' }}>\n              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>\n                <span>إجمالي الإيداعات:</span>\n                <strong style={{ color: '#28a745' }}>{formatCurrency(reportData.deposits)}</strong>\n              </div>\n              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>\n                <span>إجمالي المسحوبات:</span>\n                <strong style={{ color: '#dc3545' }}>{formatCurrency(reportData.withdrawals)}</strong>\n              </div>\n              <hr />\n              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '18px' }}>\n                <span>رصيد الخزينة:</span>\n                <strong style={{ color: '#007bff' }}>\n                  {formatCurrency(reportData.deposits - reportData.withdrawals)}\n                </strong>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGN,QAAQ,CAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC;EAEpD,MAAMY,UAAU,GAAG;IACjBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;MACRC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOC,MAAM,CAACC,MAAM,CAACpB,UAAU,CAACE,QAAQ,CAAC,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,EAAE,CAAC,CAAC;EACtF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOxB,UAAU,CAACC,MAAM,GAAGiB,gBAAgB,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBC,KAAK,CAAC,mCAAmC,CAAC;EAC5C,CAAC;EAED,oBACEpC,OAAA;IAAKqC,SAAS,EAAC,SAAS;IAAAC,QAAA,eACtBtC,OAAA;MAAKqC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBtC,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtC,OAAA;UAAIqC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD1C,OAAA;UAAKyB,KAAK,EAAE;YAAEkB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBAC3CtC,OAAA;YACE6C,KAAK,EAAE1C,aAAc;YACrB2C,QAAQ,EAAGC,CAAC,IAAK3C,gBAAgB,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClDR,SAAS,EAAC,YAAY;YACtBZ,KAAK,EAAE;cAAEwB,KAAK,EAAE;YAAO,CAAE;YAAAX,QAAA,gBAEzBtC,OAAA;cAAQ6C,KAAK,EAAC,SAAS;cAAAP,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3C1C,OAAA;cAAQ6C,KAAK,EAAC,SAAS;cAAAP,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C1C,OAAA;cAAQ6C,KAAK,EAAC,SAAS;cAAAP,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAET1C,OAAA;YACE6C,KAAK,EAAErC,UAAW;YAClBsC,QAAQ,EAAGC,CAAC,IAAKtC,aAAa,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CR,SAAS,EAAC,YAAY;YACtBZ,KAAK,EAAE;cAAEwB,KAAK,EAAE;YAAO,CAAE;YAAAX,QAAA,gBAEzBtC,OAAA;cAAQ6C,KAAK,EAAC,0BAAM;cAAAP,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC1C,OAAA;cAAQ6C,KAAK,EAAC,4CAAS;cAAAP,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C1C,OAAA;cAAQ6C,KAAK,EAAC,4CAAS;cAAAP,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAET1C,OAAA;YAAQqC,SAAS,EAAC,iBAAiB;YAACa,OAAO,EAAEf,WAAY;YAAAG,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAACZ,KAAK,EAAE;UAAE0B,YAAY,EAAE;QAAO,CAAE;QAAAb,QAAA,gBAC1DtC,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtC,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAACZ,KAAK,EAAE;cAAE2B,KAAK,EAAE;YAAU,CAAE;YAAAd,QAAA,EACrDjB,cAAc,CAACX,UAAU,CAACC,MAAM;UAAC;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtC,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAACZ,KAAK,EAAE;cAAE2B,KAAK,EAAE;YAAU,CAAE;YAAAd,QAAA,EACrDjB,cAAc,CAACO,gBAAgB,CAAC,CAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtC,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAACZ,KAAK,EAAE;cAAE2B,KAAK,EAAElB,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG;YAAU,CAAE;YAAAI,QAAA,EACvFjB,cAAc,CAACa,YAAY,CAAC,CAAC;UAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKyB,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEU,mBAAmB,EAAE,SAAS;UAAET,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBAC3EtC,OAAA;UAAKqC,SAAS,EAAC,MAAM;UAACZ,KAAK,EAAE;YAAE6B,MAAM,EAAE;UAAE,CAAE;UAAAhB,QAAA,gBACzCtC,OAAA;YAAIyB,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAO,CAAE;YAAAb,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzD1C,OAAA;YAAKqC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BtC,OAAA;cAAOqC,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACtBtC,OAAA;gBAAAsC,QAAA,eACEtC,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB1C,OAAA;oBAAAsC,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf1C,OAAA;oBAAAsC,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR1C,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACC,SAAS;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACC,SAAS,GAAGe,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACE,SAAS;kBAAC;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACE,SAAS,GAAGc,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACG,MAAM;kBAAC;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACG,MAAM,GAAGa,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACI,KAAK;kBAAC;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACI,KAAK,GAAGY,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACK,OAAO;kBAAC;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACK,OAAO,GAAGW,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACL1C,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAAI;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzB1C,OAAA;oBAAAsC,QAAA,EAAKjB,cAAc,CAACX,UAAU,CAACE,QAAQ,CAACM,SAAS;kBAAC;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD1C,OAAA;oBAAAsC,QAAA,GAAK,CAAE5B,UAAU,CAACE,QAAQ,CAACM,SAAS,GAAGU,gBAAgB,CAAC,CAAC,GAAI,GAAG,EAAE2B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,MAAM;UAACZ,KAAK,EAAE;YAAE6B,MAAM,EAAE;UAAE,CAAE;UAAAhB,QAAA,gBACzCtC,OAAA;YAAIyB,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAO,CAAE;YAAAb,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtD1C,OAAA;YAAKyB,KAAK,EAAE;cAAE+B,OAAO,EAAE;YAAO,CAAE;YAAAlB,QAAA,gBAC9BtC,OAAA;cAAKyB,KAAK,EAAE;gBAAE0B,YAAY,EAAE,MAAM;gBAAER,OAAO,EAAE,MAAM;gBAAEc,cAAc,EAAE;cAAgB,CAAE;cAAAnB,QAAA,gBACrFtC,OAAA;gBAAAsC,QAAA,EAAM;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9B1C,OAAA;gBAAQyB,KAAK,EAAE;kBAAE2B,KAAK,EAAE;gBAAU,CAAE;gBAAAd,QAAA,EAAEjB,cAAc,CAACX,UAAU,CAACS,QAAQ;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACN1C,OAAA;cAAKyB,KAAK,EAAE;gBAAE0B,YAAY,EAAE,MAAM;gBAAER,OAAO,EAAE,MAAM;gBAAEc,cAAc,EAAE;cAAgB,CAAE;cAAAnB,QAAA,gBACrFtC,OAAA;gBAAAsC,QAAA,EAAM;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9B1C,OAAA;gBAAQyB,KAAK,EAAE;kBAAE2B,KAAK,EAAE;gBAAU,CAAE;gBAAAd,QAAA,EAAEjB,cAAc,CAACX,UAAU,CAACU,WAAW;cAAC;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACN1C,OAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN1C,OAAA;cAAKyB,KAAK,EAAE;gBAAEkB,OAAO,EAAE,MAAM;gBAAEc,cAAc,EAAE,eAAe;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAApB,QAAA,gBACjFtC,OAAA;gBAAAsC,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B1C,OAAA;gBAAQyB,KAAK,EAAE;kBAAE2B,KAAK,EAAE;gBAAU,CAAE;gBAAAd,QAAA,EACjCjB,cAAc,CAACX,UAAU,CAACS,QAAQ,GAAGT,UAAU,CAACU,WAAW;cAAC;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAxKID,OAAO;AAAA0D,EAAA,GAAP1D,OAAO;AA0Kb,eAAeA,OAAO;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}