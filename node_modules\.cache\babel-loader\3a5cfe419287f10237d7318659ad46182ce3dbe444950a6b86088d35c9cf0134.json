{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  _s();\n  const location = useLocation();\n  const menuItems = [{\n    path: '/',\n    label: 'لوحة التحكم',\n    icon: '📊'\n  }, {\n    path: '/account-statements',\n    label: 'كشف الحساب',\n    icon: '📋'\n  }, {\n    path: '/warehouse-purchases',\n    label: 'مشتريات المخزن',\n    icon: '📦'\n  }, {\n    path: '/transport-expenses',\n    label: 'مصروفات النقل',\n    icon: '🚛'\n  }, {\n    path: '/living-expenses',\n    label: 'مصروفات المعيشة',\n    icon: '🏠'\n  }, {\n    path: '/paint-expenses',\n    label: 'مصروفات الطلاء',\n    icon: '🎨'\n  }, {\n    path: '/factory-expenses',\n    label: 'مصروفات المصنع',\n    icon: '🏭'\n  }, {\n    path: '/employee-withdrawals',\n    label: 'مسحوبات الموظفين',\n    icon: '👥'\n  }, {\n    path: '/treasury-deposits',\n    label: 'إيداعات الخزينة',\n    icon: '💰'\n  }, {\n    path: '/reports',\n    label: 'التقارير',\n    icon: '📈'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 1.0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"sidebar-menu\",\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n        to: item.path,\n        className: `menu-item ${location.pathname === item.path ? 'active' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"menu-icon\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), item.label]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Sidebar", "_s", "location", "menuItems", "path", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "pathname", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Sidebar = () => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      path: '/',\n      label: 'لوحة التحكم',\n      icon: '📊'\n    },\n    {\n      path: '/account-statements',\n      label: 'كشف الحساب',\n      icon: '📋'\n    },\n    {\n      path: '/warehouse-purchases',\n      label: 'مشتريات المخزن',\n      icon: '📦'\n    },\n    {\n      path: '/transport-expenses',\n      label: 'مصروفات النقل',\n      icon: '🚛'\n    },\n    {\n      path: '/living-expenses',\n      label: 'مصروفات المعيشة',\n      icon: '🏠'\n    },\n    {\n      path: '/paint-expenses',\n      label: 'مصروفات الطلاء',\n      icon: '🎨'\n    },\n    {\n      path: '/factory-expenses',\n      label: 'مصروفات المصنع',\n      icon: '🏭'\n    },\n    {\n      path: '/employee-withdrawals',\n      label: 'مسحوبات الموظفين',\n      icon: '👥'\n    },\n    {\n      path: '/treasury-deposits',\n      label: 'إيداعات الخزينة',\n      icon: '💰'\n    },\n    {\n      path: '/reports',\n      label: 'التقارير',\n      icon: '📈'\n    }\n  ];\n\n  return (\n    <div className=\"sidebar\">\n      <div className=\"sidebar-header\">\n        <h2>نظام المحاسبة</h2>\n        <p>الإصدار 1.0</p>\n      </div>\n      \n      <nav className=\"sidebar-menu\">\n        {menuItems.map((item) => (\n          <Link\n            key={item.path}\n            to={item.path}\n            className={`menu-item ${location.pathname === item.path ? 'active' : ''}`}\n          >\n            <span className=\"menu-icon\">{item.icon}</span>\n            {item.label}\n          </Link>\n        ))}\n      </nav>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACR,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBT,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BT,OAAA;QAAAS,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBb,OAAA;QAAAS,QAAA,EAAG;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENb,OAAA;MAAKQ,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BL,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAClBf,OAAA,CAACH,IAAI;QAEHmB,EAAE,EAAED,IAAI,CAACV,IAAK;QACdG,SAAS,EAAE,aAAaL,QAAQ,CAACc,QAAQ,KAAKF,IAAI,CAACV,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAI,QAAA,gBAE1ET,OAAA;UAAMQ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEM,IAAI,CAACR;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC7CE,IAAI,CAACT,KAAK;MAAA,GALNS,IAAI,CAACV,IAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA7EID,OAAO;EAAA,QACMH,WAAW;AAAA;AAAAoB,EAAA,GADxBjB,OAAO;AA+Eb,eAAeA,OAAO;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}