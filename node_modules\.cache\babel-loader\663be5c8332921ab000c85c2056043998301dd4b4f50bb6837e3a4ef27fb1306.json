{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\AccountStatements.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountStatements = () => {\n  _s();\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([{\n      id: 1,\n      date: '2024-01-15',\n      description: 'إيداع نقدي',\n      debit: 0,\n      credit: 25000,\n      balance: 25000,\n      accountType: 'خزينة'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      description: 'مشتريات مواد خام',\n      debit: 8500,\n      credit: 0,\n      balance: 16500,\n      accountType: 'مخزن'\n    }, {\n      id: 3,\n      date: '2024-01-13',\n      description: 'مصروفات نقل',\n      debit: 3200,\n      credit: 0,\n      balance: 13300,\n      accountType: 'نقل'\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"account-statements\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#dc3545'\n          },\n          children: formatCurrency(getTotalDebit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#28a745'\n          },\n          children: formatCurrency(getTotalCredit())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u062F\\u0627\\u0626\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#007bff'\n          },\n          children: formatCurrency(getCurrentBalance())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0643\\u0634\\u0641 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0642\\u064A\\u062F \\u062C\\u062F\\u064A\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          marginBottom: '20px',\n          padding: '20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"date\",\n              value: formData.date,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"accountType\",\n              value: formData.accountType,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0639\\u0627\\u0645\",\n                children: \"\\u0639\\u0627\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u062E\\u0632\\u064A\\u0646\\u0629\",\n                children: \"\\u062E\\u0632\\u064A\\u0646\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u062E\\u0632\\u0646\",\n                children: \"\\u0645\\u062E\\u0632\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0646\\u0642\\u0644\",\n                children: \"\\u0646\\u0642\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0639\\u064A\\u0634\\u0629\",\n                children: \"\\u0645\\u0639\\u064A\\u0634\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0637\\u0644\\u0627\\u0621\",\n                children: \"\\u0637\\u0644\\u0627\\u0621\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\\u0645\\u0635\\u0646\\u0639\",\n                children: \"\\u0645\\u0635\\u0646\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"description\",\n            value: formData.description,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0645\\u0639\\u0627\\u0645\\u0644\\u0629\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0645\\u062F\\u064A\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"debit\",\n              value: formData.debit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062F\\u0627\\u0626\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"credit\",\n              value: formData.credit,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"0.00\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0642\\u064A\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => setShowForm(false),\n            children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0645\\u062F\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u062F\\u0627\\u0626\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: statements.map(statement => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(statement.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: statement.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-info\",\n                  children: statement.accountType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.debit > 0 ? '#dc3545' : '#666'\n                },\n                children: statement.debit > 0 ? formatCurrency(statement.debit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  color: statement.credit > 0 ? '#28a745' : '#666'\n                },\n                children: statement.credit > 0 ? formatCurrency(statement.credit) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: statement.balance >= 0 ? '#28a745' : '#dc3545'\n                },\n                children: formatCurrency(statement.balance)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, statement.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountStatements, \"7bAKIV1eHvpDzANl8d6G50mA5Qw=\");\n_c = AccountStatements;\nexport default AccountStatements;\nvar _c;\n$RefreshReg$(_c, \"AccountStatements\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AccountStatements", "_s", "statements", "setStatements", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "description", "debit", "credit", "accountType", "id", "balance", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newStatement", "length", "parseFloat", "calculateNewBalance", "lastBalance", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getTotalDebit", "reduce", "sum", "statement", "getTotalCredit", "getCurrentBalance", "className", "children", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "display", "gap", "map", "toLocaleDateString", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/AccountStatements.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst AccountStatements = () => {\n  const [statements, setStatements] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    description: '',\n    debit: '',\n    credit: '',\n    accountType: 'عام'\n  });\n\n  useEffect(() => {\n    // جلب البيانات من قاعدة البيانات\n    // مؤقتاً سنستخدم بيانات تجريبية\n    setStatements([\n      {\n        id: 1,\n        date: '2024-01-15',\n        description: 'إيداع نقدي',\n        debit: 0,\n        credit: 25000,\n        balance: 25000,\n        accountType: 'خزينة'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        description: 'مشتريات مواد خام',\n        debit: 8500,\n        credit: 0,\n        balance: 16500,\n        accountType: 'مخزن'\n      },\n      {\n        id: 3,\n        date: '2024-01-13',\n        description: 'مصروفات نقل',\n        debit: 3200,\n        credit: 0,\n        balance: 13300,\n        accountType: 'نقل'\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newStatement = {\n      id: statements.length + 1,\n      ...formData,\n      debit: parseFloat(formData.debit) || 0,\n      credit: parseFloat(formData.credit) || 0,\n      balance: calculateNewBalance()\n    };\n\n    setStatements(prev => [newStatement, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      description: '',\n      debit: '',\n      credit: '',\n      accountType: 'عام'\n    });\n    setShowForm(false);\n  };\n\n  const calculateNewBalance = () => {\n    const lastBalance = statements.length > 0 ? statements[0].balance : 0;\n    const debit = parseFloat(formData.debit) || 0;\n    const credit = parseFloat(formData.credit) || 0;\n    return lastBalance + credit - debit;\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getTotalDebit = () => {\n    return statements.reduce((sum, statement) => sum + statement.debit, 0);\n  };\n\n  const getTotalCredit = () => {\n    return statements.reduce((sum, statement) => sum + statement.credit, 0);\n  };\n\n  const getCurrentBalance = () => {\n    return getTotalCredit() - getTotalDebit();\n  };\n\n  return (\n    <div className=\"account-statements\">\n      {/* ملخص الحساب */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#dc3545' }}>\n            {formatCurrency(getTotalDebit())}\n          </div>\n          <div className=\"stat-label\">إجمالي المدين</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#28a745' }}>\n            {formatCurrency(getTotalCredit())}\n          </div>\n          <div className=\"stat-label\">إجمالي الدائن</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#007bff' }}>\n            {formatCurrency(getCurrentBalance())}\n          </div>\n          <div className=\"stat-label\">الرصيد الحالي</div>\n        </div>\n      </div>\n\n      {/* كشف الحساب */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">كشف الحساب</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة قيد جديد\n          </button>\n        </div>\n\n        {/* نموذج إضافة قيد */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">نوع الحساب</label>\n                <select\n                  name=\"accountType\"\n                  value={formData.accountType}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                >\n                  <option value=\"عام\">عام</option>\n                  <option value=\"خزينة\">خزينة</option>\n                  <option value=\"مخزن\">مخزن</option>\n                  <option value=\"نقل\">نقل</option>\n                  <option value=\"معيشة\">معيشة</option>\n                  <option value=\"طلاء\">طلاء</option>\n                  <option value=\"مصنع\">مصنع</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">الوصف</label>\n              <input\n                type=\"text\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"وصف المعاملة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">مدين</label>\n                <input\n                  type=\"number\"\n                  name=\"debit\"\n                  value={formData.debit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">دائن</label>\n                <input\n                  type=\"number\"\n                  name=\"credit\"\n                  value={formData.credit}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"0.00\"\n                  step=\"0.01\"\n                />\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ القيد\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول كشف الحساب */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>الوصف</th>\n                <th>نوع الحساب</th>\n                <th>مدين</th>\n                <th>دائن</th>\n                <th>الرصيد</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {statements.map((statement) => (\n                <tr key={statement.id}>\n                  <td>{new Date(statement.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{statement.description}</td>\n                  <td>\n                    <span className=\"badge badge-info\">{statement.accountType}</span>\n                  </td>\n                  <td style={{ color: statement.debit > 0 ? '#dc3545' : '#666' }}>\n                    {statement.debit > 0 ? formatCurrency(statement.debit) : '-'}\n                  </td>\n                  <td style={{ color: statement.credit > 0 ? '#28a745' : '#666' }}>\n                    {statement.credit > 0 ? formatCurrency(statement.credit) : '-'}\n                  </td>\n                  <td style={{ \n                    fontWeight: 'bold',\n                    color: statement.balance >= 0 ? '#28a745' : '#dc3545'\n                  }}>\n                    {formatCurrency(statement.balance)}\n                  </td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AccountStatements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACd;IACA;IACAM,aAAa,CAAC,CACZ;MACEa,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,KAAK;MACbG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLR,IAAI,EAAE,YAAY;MAClBI,WAAW,EAAE,aAAa;MAC1BC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTG,OAAO,EAAE,KAAK;MACdF,WAAW,EAAE;IACf,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,YAAY,GAAG;MACnBV,EAAE,EAAEd,UAAU,CAACyB,MAAM,GAAG,CAAC;MACzB,GAAGrB,QAAQ;MACXO,KAAK,EAAEe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;MACtCC,MAAM,EAAEc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;MACxCG,OAAO,EAAEY,mBAAmB,CAAC;IAC/B,CAAC;IAED1B,aAAa,CAACoB,IAAI,IAAI,CAACG,YAAY,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC9ChB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE;IACf,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG5B,UAAU,CAACyB,MAAM,GAAG,CAAC,GAAGzB,UAAU,CAAC,CAAC,CAAC,CAACe,OAAO,GAAG,CAAC;IACrE,MAAMJ,KAAK,GAAGe,UAAU,CAACtB,QAAQ,CAACO,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGc,UAAU,CAACtB,QAAQ,CAACQ,MAAM,CAAC,IAAI,CAAC;IAC/C,OAAOgB,WAAW,GAAGhB,MAAM,GAAGD,KAAK;EACrC,CAAC;EAED,MAAMkB,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOpC,UAAU,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAAC5B,KAAK,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,MAAM6B,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOxC,UAAU,CAACqC,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,GAAGC,SAAS,CAAC3B,MAAM,EAAE,CAAC,CAAC;EACzE,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,OAAOD,cAAc,CAAC,CAAC,GAAGJ,aAAa,CAAC,CAAC;EAC3C,CAAC;EAED,oBACEvC,OAAA;IAAK6C,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjC9C,OAAA;MAAK6C,SAAS,EAAC,YAAY;MAACT,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAC1D9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACT,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDd,cAAc,CAACO,aAAa,CAAC,CAAC;QAAC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACT,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDd,cAAc,CAACW,cAAc,CAAC,CAAC;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACT,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDd,cAAc,CAACY,iBAAiB,CAAC,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAK6C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAI6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CpD,OAAA;UACE6C,SAAS,EAAC,iBAAiB;UAC3BQ,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAAyC,QAAA,EACvC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/C,QAAQ,iBACPL,OAAA;QAAMsD,QAAQ,EAAE7B,YAAa;QAACW,KAAK,EAAE;UAAEW,YAAY,EAAE,MAAM;UAAEQ,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAX,QAAA,gBAC9H9C,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CpD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXrC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEf,QAAQ,CAACE,IAAK;cACrBkD,QAAQ,EAAExC,iBAAkB;cAC5B0B,SAAS,EAAC,YAAY;cACtBe,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDpD,OAAA;cACEqB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEf,QAAQ,CAACS,WAAY;cAC5B2C,QAAQ,EAAExC,iBAAkB;cAC5B0B,SAAS,EAAC,YAAY;cACtBe,QAAQ;cAAAd,QAAA,gBAER9C,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAAwB,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCpD,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAAwB,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCpD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAwB,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCpD,OAAA;gBAAQsB,KAAK,EAAC,oBAAK;gBAAAwB,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCpD,OAAA;gBAAQsB,KAAK,EAAC,gCAAO;gBAAAwB,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCpD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAwB,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCpD,OAAA;gBAAQsB,KAAK,EAAC,0BAAM;gBAAAwB,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9C,OAAA;YAAO6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CpD,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXrC,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEf,QAAQ,CAACM,WAAY;YAC5B8C,QAAQ,EAAExC,iBAAkB;YAC5B0B,SAAS,EAAC,YAAY;YACtBgB,WAAW,EAAC,qEAAc;YAC1BD,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CpD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbrC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEf,QAAQ,CAACO,KAAM;cACtB6C,QAAQ,EAAExC,iBAAkB;cAC5B0B,SAAS,EAAC,YAAY;cACtBgB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CpD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbrC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEf,QAAQ,CAACQ,MAAO;cACvB4C,QAAQ,EAAExC,iBAAkB;cAC5B0B,SAAS,EAAC,YAAY;cACtBgB,WAAW,EAAC,MAAM;cAClBC,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAKoC,KAAK,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAlB,QAAA,gBAC3C9C,OAAA;YAAQ0D,IAAI,EAAC,QAAQ;YAACb,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA;YACE0D,IAAI,EAAC,QAAQ;YACbb,SAAS,EAAC,mBAAmB;YAC7BQ,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,KAAK,CAAE;YAAAwC,QAAA,EACnC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAGDpD,OAAA;QAAK6C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9C,OAAA;UAAO6C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB9C,OAAA;YAAA8C,QAAA,eACE9C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpD,OAAA;gBAAA8C,QAAA,EAAI;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbpD,OAAA;gBAAA8C,QAAA,EAAI;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbpD,OAAA;gBAAA8C,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpD,OAAA;gBAAA8C,QAAA,EAAI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpD,OAAA;YAAA8C,QAAA,EACG3C,UAAU,CAAC8D,GAAG,CAAEvB,SAAS,iBACxB1C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAK,IAAIpC,IAAI,CAACgC,SAAS,CAACjC,IAAI,CAAC,CAACyD,kBAAkB,CAAC,OAAO;cAAC;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DpD,OAAA;gBAAA8C,QAAA,EAAKJ,SAAS,CAAC7B;cAAW;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChCpD,OAAA;gBAAA8C,QAAA,eACE9C,OAAA;kBAAM6C,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEJ,SAAS,CAAC1B;gBAAW;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACLpD,OAAA;gBAAIoC,KAAK,EAAE;kBAAEY,KAAK,EAAEN,SAAS,CAAC5B,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAAgC,QAAA,EAC5DJ,SAAS,CAAC5B,KAAK,GAAG,CAAC,GAAGkB,cAAc,CAACU,SAAS,CAAC5B,KAAK,CAAC,GAAG;cAAG;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACLpD,OAAA;gBAAIoC,KAAK,EAAE;kBAAEY,KAAK,EAAEN,SAAS,CAAC3B,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;gBAAO,CAAE;gBAAA+B,QAAA,EAC7DJ,SAAS,CAAC3B,MAAM,GAAG,CAAC,GAAGiB,cAAc,CAACU,SAAS,CAAC3B,MAAM,CAAC,GAAG;cAAG;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACLpD,OAAA;gBAAIoC,KAAK,EAAE;kBACT+B,UAAU,EAAE,MAAM;kBAClBnB,KAAK,EAAEN,SAAS,CAACxB,OAAO,IAAI,CAAC,GAAG,SAAS,GAAG;gBAC9C,CAAE;gBAAA4B,QAAA,EACCd,cAAc,CAACU,SAAS,CAACxB,OAAO;cAAC;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLpD,OAAA;gBAAA8C,QAAA,eACE9C,OAAA;kBAAQ6C,SAAS,EAAC,mBAAmB;kBAACT,KAAK,EAAE;oBAAEmB,OAAO,EAAE,UAAU;oBAAEa,QAAQ,EAAE;kBAAO,CAAE;kBAAAtB,QAAA,EAAC;gBAExF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAtBEV,SAAS,CAACzB,EAAE;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBjB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAtRID,iBAAiB;AAAAoE,EAAA,GAAjBpE,iBAAiB;AAwRvB,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}