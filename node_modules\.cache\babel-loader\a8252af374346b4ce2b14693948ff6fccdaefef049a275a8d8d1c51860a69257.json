{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\FactoryExpenses.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FactoryExpenses = () => {\n  _s();\n  const [expenses, setExpenses] = useState([{\n    id: 1,\n    date: '2024-01-15',\n    expenseCategory: 'صيانة',\n    description: 'صيانة دورية للآلات',\n    amount: 5500,\n    department: 'الإنتاج',\n    approvedBy: 'مدير المصنع',\n    receiptNumber: 'FAC-001'\n  }]);\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"factory-expenses\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0635\\u0631\\u0648\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0646\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0635\\u0631\\u0648\\u0641 \\u0645\\u0635\\u0646\\u0639\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0641\\u0626\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0642\\u0633\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0645\\u0639\\u062A\\u0645\\u062F \\u0645\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(expense.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.expenseCategory\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#dc3545'\n                },\n                children: formatCurrency(expense.amount)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.department\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.approvedBy\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: expense.receiptNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)]\n            }, expense.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(FactoryExpenses, \"/6mLEvOH0Twc2VALF1kj86H1GlU=\");\n_c = FactoryExpenses;\nexport default FactoryExpenses;\nvar _c;\n$RefreshReg$(_c, \"FactoryExpenses\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "FactoryExpenses", "_s", "expenses", "setExpenses", "id", "date", "expenseCategory", "description", "amount", "department", "approvedBy", "receiptNumber", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "expense", "Date", "toLocaleDateString", "fontWeight", "color", "padding", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/FactoryExpenses.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst FactoryExpenses = () => {\n  const [expenses, setExpenses] = useState([\n    {\n      id: 1,\n      date: '2024-01-15',\n      expenseCategory: 'صيانة',\n      description: 'صيانة دورية للآلات',\n      amount: 5500,\n      department: 'الإنتاج',\n      approvedBy: 'مدير المصنع',\n      receiptNumber: 'FAC-001'\n    }\n  ]);\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"factory-expenses\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مصروفات المصنع</h3>\n          <button className=\"btn btn-primary\">\n            ➕ إضافة مصروف مصنع\n          </button>\n        </div>\n\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>فئة المصروف</th>\n                <th>الوصف</th>\n                <th>المبلغ</th>\n                <th>القسم</th>\n                <th>معتمد من</th>\n                <th>رقم الإيصال</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {expenses.map((expense) => (\n                <tr key={expense.id}>\n                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{expense.expenseCategory}</td>\n                  <td>{expense.description}</td>\n                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>\n                    {formatCurrency(expense.amount)}\n                  </td>\n                  <td>{expense.department}</td>\n                  <td>{expense.approvedBy}</td>\n                  <td>{expense.receiptNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FactoryExpenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC,CACvC;IACEO,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,aAAa;IACzBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAMC,cAAc,GAAIJ,MAAM,IAAK;IACjC,OAAO,IAAIK,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACT,MAAM,CAAC;EACnB,CAAC;EAED,oBACET,OAAA;IAAKmB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BpB,OAAA;MAAKmB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBpB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpB,OAAA;UAAImB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CxB,OAAA;UAAQmB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENxB,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BpB,OAAA;UAAOmB,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBpB,OAAA;YAAAoB,QAAA,eACEpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAAoB,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBxB,OAAA;gBAAAoB,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxB,OAAA;gBAAAoB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxB,OAAA;gBAAAoB,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfxB,OAAA;gBAAAoB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxB,OAAA;gBAAAoB,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBxB,OAAA;gBAAAoB,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxB,OAAA;gBAAAoB,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxB,OAAA;YAAAoB,QAAA,EACGjB,QAAQ,CAACsB,GAAG,CAAEC,OAAO,iBACpB1B,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAAoB,QAAA,EAAK,IAAIO,IAAI,CAACD,OAAO,CAACpB,IAAI,CAAC,CAACsB,kBAAkB,CAAC,OAAO;cAAC;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DxB,OAAA;gBAAAoB,QAAA,EAAKM,OAAO,CAACnB;cAAe;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCxB,OAAA;gBAAAoB,QAAA,EAAKM,OAAO,CAAClB;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BxB,OAAA;gBAAIgB,KAAK,EAAE;kBAAEa,UAAU,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EACjDP,cAAc,CAACa,OAAO,CAACjB,MAAM;cAAC;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLxB,OAAA;gBAAAoB,QAAA,EAAKM,OAAO,CAAChB;cAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BxB,OAAA;gBAAAoB,QAAA,EAAKM,OAAO,CAACf;cAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7BxB,OAAA;gBAAAoB,QAAA,EAAKM,OAAO,CAACd;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChCxB,OAAA;gBAAAoB,QAAA,eACEpB,OAAA;kBAAQmB,SAAS,EAAC,mBAAmB;kBAACH,KAAK,EAAE;oBAAEe,OAAO,EAAE,UAAU;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAZ,QAAA,EAAC;gBAExF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAdEE,OAAO,CAACrB,EAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAef,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAtEID,eAAe;AAAAgC,EAAA,GAAfhC,eAAe;AAwErB,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}