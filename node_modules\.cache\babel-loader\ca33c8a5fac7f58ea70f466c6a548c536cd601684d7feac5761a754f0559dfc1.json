{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './components/Dashboard';\nimport AccountStatements from './components/AccountStatements';\nimport WarehousePurchases from './components/WarehousePurchases';\nimport TransportExpenses from './components/TransportExpenses';\nimport LivingExpenses from './components/LivingExpenses';\nimport PaintExpenses from './components/PaintExpenses';\nimport FactoryExpenses from './components/FactoryExpenses';\nimport EmployeeWithdrawals from './components/EmployeeWithdrawals';\nimport TreasuryDeposits from './components/TreasuryDeposits';\nimport Reports from './components/Reports';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/account-statements\",\n              element: /*#__PURE__*/_jsxDEV(AccountStatements, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/warehouse-purchases\",\n              element: /*#__PURE__*/_jsxDEV(WarehousePurchases, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 59\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/transport-expenses\",\n              element: /*#__PURE__*/_jsxDEV(TransportExpenses, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/living-expenses\",\n              element: /*#__PURE__*/_jsxDEV(LivingExpenses, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 55\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/paint-expenses\",\n              element: /*#__PURE__*/_jsxDEV(PaintExpenses, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/factory-expenses\",\n              element: /*#__PURE__*/_jsxDEV(FactoryExpenses, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 56\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/employee-withdrawals\",\n              element: /*#__PURE__*/_jsxDEV(EmployeeWithdrawals, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 60\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/treasury-deposits\",\n              element: /*#__PURE__*/_jsxDEV(TreasuryDeposits, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 57\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/reports\",\n              element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Sidebar", "Header", "Dashboard", "AccountStatements", "WarehousePurchases", "TransportExpenses", "LivingExpenses", "PaintExpenses", "FactoryExpenses", "EmployeeWithdrawals", "TreasuryDeposits", "Reports", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './components/Dashboard';\nimport AccountStatements from './components/AccountStatements';\nimport WarehousePurchases from './components/WarehousePurchases';\nimport TransportExpenses from './components/TransportExpenses';\nimport LivingExpenses from './components/LivingExpenses';\nimport PaintExpenses from './components/PaintExpenses';\nimport FactoryExpenses from './components/FactoryExpenses';\nimport EmployeeWithdrawals from './components/EmployeeWithdrawals';\nimport TreasuryDeposits from './components/TreasuryDeposits';\nimport Reports from './components/Reports';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"app\">\n        <Sidebar />\n        <div className=\"main-content\">\n          <Header />\n          <div className=\"content\">\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/account-statements\" element={<AccountStatements />} />\n              <Route path=\"/warehouse-purchases\" element={<WarehousePurchases />} />\n              <Route path=\"/transport-expenses\" element={<TransportExpenses />} />\n              <Route path=\"/living-expenses\" element={<LivingExpenses />} />\n              <Route path=\"/paint-expenses\" element={<PaintExpenses />} />\n              <Route path=\"/factory-expenses\" element={<FactoryExpenses />} />\n              <Route path=\"/employee-withdrawals\" element={<EmployeeWithdrawals />} />\n              <Route path=\"/treasury-deposits\" element={<TreasuryDeposits />} />\n              <Route path=\"/reports\" element={<Reports />} />\n            </Routes>\n          </div>\n        </div>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAAChB,MAAM;IAAAkB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA,CAACb,OAAO;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXP,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BF,OAAA,CAACZ,MAAM;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA;UAAKG,SAAS,EAAC,SAAS;UAAAD,QAAA,eACtBF,OAAA,CAACf,MAAM;YAAAiB,QAAA,gBACLF,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACX,SAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,qBAAqB;cAACC,OAAO,eAAET,OAAA,CAACV,iBAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,sBAAsB;cAACC,OAAO,eAAET,OAAA,CAACT,kBAAkB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,qBAAqB;cAACC,OAAO,eAAET,OAAA,CAACR,iBAAiB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,kBAAkB;cAACC,OAAO,eAAET,OAAA,CAACP,cAAc;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAET,OAAA,CAACN,aAAa;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,mBAAmB;cAACC,OAAO,eAAET,OAAA,CAACL,eAAe;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,uBAAuB;cAACC,OAAO,eAAET,OAAA,CAACJ,mBAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,oBAAoB;cAACC,OAAO,eAAET,OAAA,CAACH,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClEP,OAAA,CAACd,KAAK;cAACsB,IAAI,EAAC,UAAU;cAACC,OAAO,eAAET,OAAA,CAACF,OAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACG,EAAA,GAzBQT,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}