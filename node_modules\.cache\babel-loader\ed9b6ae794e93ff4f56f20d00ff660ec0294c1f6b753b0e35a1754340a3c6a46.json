{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0645\\u0646\\u0636\\u0648\\u0645\\u0629 \\u062E\\u0641\\u064A\\u0641\\u0629\\\\src\\\\components\\\\WarehousePurchases.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WarehousePurchases = () => {\n  _s();\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n  useEffect(() => {\n    // بيانات تجريبية\n    setPurchases([{\n      id: 1,\n      date: '2024-01-15',\n      itemName: 'مواد خام - حديد',\n      quantity: 100,\n      unitPrice: 50,\n      totalPrice: 5000,\n      supplier: 'شركة الحديد المتحدة',\n      invoiceNumber: 'INV-001',\n      notes: 'جودة عالية'\n    }, {\n      id: 2,\n      date: '2024-01-14',\n      itemName: 'أدوات تصنيع',\n      quantity: 25,\n      unitPrice: 120,\n      totalPrice: 3000,\n      supplier: 'مؤسسة الأدوات',\n      invoiceNumber: 'INV-002',\n      notes: ''\n    }]);\n  }, []);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"warehouse-purchases\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#007bff'\n          },\n          children: formatCurrency(getTotalValue())\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#28a745'\n          },\n          children: getTotalItems()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          style: {\n            color: '#6f42c1'\n          },\n          children: purchases.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowForm(!showForm),\n          children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A \\u062C\\u062F\\u064A\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        style: {\n          marginBottom: '20px',\n          padding: '20px',\n          backgroundColor: '#f8f9fa',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"date\",\n              value: formData.date,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"invoiceNumber\",\n              value: formData.invoiceNumber,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"itemName\",\n            value: formData.itemName,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641 \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0627\\u062F\\u0629\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"quantity\",\n              value: formData.quantity,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"unitPrice\",\n              value: formData.unitPrice,\n              onChange: handleInputChange,\n              className: \"form-input\",\n              placeholder: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\",\n              step: \"0.01\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"supplier\",\n            value: formData.supplier,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"notes\",\n            value: formData.notes,\n            onChange: handleInputChange,\n            className: \"form-input\",\n            placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\",\n            rows: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), formData.quantity && formData.unitPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-info\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A: \", formatCurrency(formData.quantity * formData.unitPrice)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDCBE \\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: () => setShowForm(false),\n            children: \"\\u274C \\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0635\\u0646\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: purchases.map(purchase => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(purchase.date).toLocaleDateString('ar-SA')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.itemName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(purchase.unitPrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#007bff'\n                },\n                children: formatCurrency(purchase.totalPrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.supplier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: purchase.invoiceNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '5px 10px',\n                    fontSize: '12px'\n                  },\n                  children: \"\\u270F\\uFE0F \\u062A\\u0639\\u062F\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, purchase.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(WarehousePurchases, \"9CsT2i5s2pYPiZ+af+pPz+tztBQ=\");\n_c = WarehousePurchases;\nexport default WarehousePurchases;\nvar _c;\n$RefreshReg$(_c, \"WarehousePurchases\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "WarehousePurchases", "_s", "purchases", "setPurchases", "showForm", "setShowForm", "formData", "setFormData", "date", "Date", "toISOString", "split", "itemName", "quantity", "unitPrice", "supplier", "invoiceNumber", "notes", "id", "totalPrice", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "newPurchase", "length", "parseInt", "parseFloat", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getTotalValue", "reduce", "sum", "purchase", "getTotalItems", "className", "children", "marginBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "padding", "backgroundColor", "borderRadius", "type", "onChange", "required", "placeholder", "step", "rows", "display", "gap", "map", "toLocaleDateString", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/منضومة خفيفة/src/components/WarehousePurchases.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst WarehousePurchases = () => {\n  const [purchases, setPurchases] = useState([]);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState({\n    date: new Date().toISOString().split('T')[0],\n    itemName: '',\n    quantity: '',\n    unitPrice: '',\n    supplier: '',\n    invoiceNumber: '',\n    notes: ''\n  });\n\n  useEffect(() => {\n    // بيانات تجريبية\n    setPurchases([\n      {\n        id: 1,\n        date: '2024-01-15',\n        itemName: 'مواد خام - حديد',\n        quantity: 100,\n        unitPrice: 50,\n        totalPrice: 5000,\n        supplier: 'شركة الحديد المتحدة',\n        invoiceNumber: 'INV-001',\n        notes: 'جودة عالية'\n      },\n      {\n        id: 2,\n        date: '2024-01-14',\n        itemName: 'أدوات تصنيع',\n        quantity: 25,\n        unitPrice: 120,\n        totalPrice: 3000,\n        supplier: 'مؤسسة الأدوات',\n        invoiceNumber: 'INV-002',\n        notes: ''\n      }\n    ]);\n  }, []);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    const newPurchase = {\n      id: purchases.length + 1,\n      ...formData,\n      quantity: parseInt(formData.quantity),\n      unitPrice: parseFloat(formData.unitPrice),\n      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)\n    };\n\n    setPurchases(prev => [newPurchase, ...prev]);\n    setFormData({\n      date: new Date().toISOString().split('T')[0],\n      itemName: '',\n      quantity: '',\n      unitPrice: '',\n      supplier: '',\n      invoiceNumber: '',\n      notes: ''\n    });\n    setShowForm(false);\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getTotalValue = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);\n  };\n\n  const getTotalItems = () => {\n    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);\n  };\n\n  return (\n    <div className=\"warehouse-purchases\">\n      {/* إحصائيات المخزن */}\n      <div className=\"stats-grid\" style={{ marginBottom: '30px' }}>\n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#007bff' }}>\n            {formatCurrency(getTotalValue())}\n          </div>\n          <div className=\"stat-label\">إجمالي قيمة المشتريات</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#28a745' }}>\n            {getTotalItems()}\n          </div>\n          <div className=\"stat-label\">إجمالي الكمية</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-value\" style={{ color: '#6f42c1' }}>\n            {purchases.length}\n          </div>\n          <div className=\"stat-label\">عدد المشتريات</div>\n        </div>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">مشتريات المخزن</h3>\n          <button \n            className=\"btn btn-primary\"\n            onClick={() => setShowForm(!showForm)}\n          >\n            ➕ إضافة مشتريات جديدة\n          </button>\n        </div>\n\n        {/* نموذج إضافة مشتريات */}\n        {showForm && (\n          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">التاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"date\"\n                  value={formData.date}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">رقم الفاتورة</label>\n                <input\n                  type=\"text\"\n                  name=\"invoiceNumber\"\n                  value={formData.invoiceNumber}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"رقم الفاتورة\"\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">اسم الصنف</label>\n              <input\n                type=\"text\"\n                name=\"itemName\"\n                value={formData.itemName}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم الصنف أو المادة\"\n                required\n              />\n            </div>\n\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">الكمية</label>\n                <input\n                  type=\"number\"\n                  name=\"quantity\"\n                  value={formData.quantity}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"الكمية\"\n                  required\n                />\n              </div>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">سعر الوحدة</label>\n                <input\n                  type=\"number\"\n                  name=\"unitPrice\"\n                  value={formData.unitPrice}\n                  onChange={handleInputChange}\n                  className=\"form-input\"\n                  placeholder=\"سعر الوحدة\"\n                  step=\"0.01\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">المورد</label>\n              <input\n                type=\"text\"\n                name=\"supplier\"\n                value={formData.supplier}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"اسم المورد\"\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label className=\"form-label\">ملاحظات</label>\n              <textarea\n                name=\"notes\"\n                value={formData.notes}\n                onChange={handleInputChange}\n                className=\"form-input\"\n                placeholder=\"ملاحظات إضافية\"\n                rows=\"3\"\n              />\n            </div>\n\n            {formData.quantity && formData.unitPrice && (\n              <div className=\"alert alert-info\">\n                <strong>إجمالي المبلغ: {formatCurrency(formData.quantity * formData.unitPrice)}</strong>\n              </div>\n            )}\n\n            <div style={{ display: 'flex', gap: '10px' }}>\n              <button type=\"submit\" className=\"btn btn-primary\">\n                💾 حفظ المشتريات\n              </button>\n              <button \n                type=\"button\" \n                className=\"btn btn-secondary\"\n                onClick={() => setShowForm(false)}\n              >\n                ❌ إلغاء\n              </button>\n            </div>\n          </form>\n        )}\n\n        {/* جدول المشتريات */}\n        <div className=\"table-container\">\n          <table className=\"table\">\n            <thead>\n              <tr>\n                <th>التاريخ</th>\n                <th>اسم الصنف</th>\n                <th>الكمية</th>\n                <th>سعر الوحدة</th>\n                <th>إجمالي المبلغ</th>\n                <th>المورد</th>\n                <th>رقم الفاتورة</th>\n                <th>إجراءات</th>\n              </tr>\n            </thead>\n            <tbody>\n              {purchases.map((purchase) => (\n                <tr key={purchase.id}>\n                  <td>{new Date(purchase.date).toLocaleDateString('ar-SA')}</td>\n                  <td>{purchase.itemName}</td>\n                  <td>{purchase.quantity}</td>\n                  <td>{formatCurrency(purchase.unitPrice)}</td>\n                  <td style={{ fontWeight: 'bold', color: '#007bff' }}>\n                    {formatCurrency(purchase.totalPrice)}\n                  </td>\n                  <td>{purchase.supplier}</td>\n                  <td>{purchase.invoiceNumber}</td>\n                  <td>\n                    <button className=\"btn btn-secondary\" style={{ padding: '5px 10px', fontSize: '12px' }}>\n                      ✏️ تعديل\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WarehousePurchases;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFpB,SAAS,CAAC,MAAM;IACd;IACAM,YAAY,CAAC,CACX;MACEe,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,iBAAiB;MAC3BC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,EAAE;MACbK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,qBAAqB;MAC/BC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE;IACT,CAAC,EACD;MACEC,EAAE,EAAE,CAAC;MACLV,IAAI,EAAE,YAAY;MAClBI,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdK,UAAU,EAAE,IAAI;MAChBJ,QAAQ,EAAE,eAAe;MACzBC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE;IACT,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,MAAMC,WAAW,GAAG;MAClBV,EAAE,EAAEhB,SAAS,CAAC2B,MAAM,GAAG,CAAC;MACxB,GAAGvB,QAAQ;MACXO,QAAQ,EAAEiB,QAAQ,CAACxB,QAAQ,CAACO,QAAQ,CAAC;MACrCC,SAAS,EAAEiB,UAAU,CAACzB,QAAQ,CAACQ,SAAS,CAAC;MACzCK,UAAU,EAAEW,QAAQ,CAACxB,QAAQ,CAACO,QAAQ,CAAC,GAAGkB,UAAU,CAACzB,QAAQ,CAACQ,SAAS;IACzE,CAAC;IAEDX,YAAY,CAACsB,IAAI,IAAI,CAACG,WAAW,EAAE,GAAGH,IAAI,CAAC,CAAC;IAC5ClB,WAAW,CAAC;MACVC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFZ,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM2B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOrC,SAAS,CAACsC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAACvB,UAAU,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMwB,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOzC,SAAS,CAACsC,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,GAAGC,QAAQ,CAAC7B,QAAQ,EAAE,CAAC,CAAC;EACxE,CAAC;EAED,oBACEd,OAAA;IAAK6C,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElC9C,OAAA;MAAK6C,SAAS,EAAC,YAAY;MAACR,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAC1D9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACR,KAAK,EAAE;YAAEW,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDb,cAAc,CAACO,aAAa,CAAC,CAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACR,KAAK,EAAE;YAAEW,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrDF,aAAa,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAACR,KAAK,EAAE;YAAEW,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACrD3C,SAAS,CAAC2B;QAAM;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpD,OAAA;MAAK6C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAI6C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CpD,OAAA;UACE6C,SAAS,EAAC,iBAAiB;UAC3BQ,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,CAACD,QAAQ,CAAE;UAAAyC,QAAA,EACvC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL/C,QAAQ,iBACPL,OAAA;QAAMsD,QAAQ,EAAE3B,YAAa;QAACU,KAAK,EAAE;UAAEU,YAAY,EAAE,MAAM;UAAEQ,OAAO,EAAE,MAAM;UAAEC,eAAe,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAX,QAAA,gBAC9H9C,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CpD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXnC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEjB,QAAQ,CAACE,IAAK;cACrBkD,QAAQ,EAAEtC,iBAAkB;cAC5BwB,SAAS,EAAC,YAAY;cACtBe,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDpD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXnC,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAEjB,QAAQ,CAACU,aAAc;cAC9B0C,QAAQ,EAAEtC,iBAAkB;cAC5BwB,SAAS,EAAC,YAAY;cACtBgB,WAAW,EAAC;YAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9C,OAAA;YAAO6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/CpD,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXnC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEjB,QAAQ,CAACM,QAAS;YACzB8C,QAAQ,EAAEtC,iBAAkB;YAC5BwB,SAAS,EAAC,YAAY;YACtBgB,WAAW,EAAC,qGAAqB;YACjCD,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9C,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CpD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbnC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEjB,QAAQ,CAACO,QAAS;cACzB6C,QAAQ,EAAEtC,iBAAkB;cAC5BwB,SAAS,EAAC,YAAY;cACtBgB,WAAW,EAAC,sCAAQ;cACpBD,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpD,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9C,OAAA;cAAO6C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDpD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbnC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEjB,QAAQ,CAACQ,SAAU;cAC1B4C,QAAQ,EAAEtC,iBAAkB;cAC5BwB,SAAS,EAAC,YAAY;cACtBgB,WAAW,EAAC,yDAAY;cACxBC,IAAI,EAAC,MAAM;cACXF,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9C,OAAA;YAAO6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CpD,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXnC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEjB,QAAQ,CAACS,QAAS;YACzB2C,QAAQ,EAAEtC,iBAAkB;YAC5BwB,SAAS,EAAC,YAAY;YACtBgB,WAAW,EAAC;UAAY;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9C,OAAA;YAAO6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CpD,OAAA;YACEuB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEjB,QAAQ,CAACW,KAAM;YACtByC,QAAQ,EAAEtC,iBAAkB;YAC5BwB,SAAS,EAAC,YAAY;YACtBgB,WAAW,EAAC,iFAAgB;YAC5BE,IAAI,EAAC;UAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL7C,QAAQ,CAACO,QAAQ,IAAIP,QAAQ,CAACQ,SAAS,iBACtCf,OAAA;UAAK6C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B9C,OAAA;YAAA8C,QAAA,GAAQ,6EAAe,EAACb,cAAc,CAAC1B,QAAQ,CAACO,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAAC;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CACN,eAEDpD,OAAA;UAAKqC,KAAK,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,gBAC3C9C,OAAA;YAAQ0D,IAAI,EAAC,QAAQ;YAACb,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAElD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA;YACE0D,IAAI,EAAC,QAAQ;YACbb,SAAS,EAAC,mBAAmB;YAC7BQ,OAAO,EAAEA,CAAA,KAAM/C,WAAW,CAAC,KAAK,CAAE;YAAAwC,QAAA,EACnC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAGDpD,OAAA;QAAK6C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9C,OAAA;UAAO6C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB9C,OAAA;YAAA8C,QAAA,eACE9C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpD,OAAA;gBAAA8C,QAAA,EAAI;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpD,OAAA;gBAAA8C,QAAA,EAAI;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBpD,OAAA;gBAAA8C,QAAA,EAAI;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpD,OAAA;YAAA8C,QAAA,EACG3C,SAAS,CAAC+D,GAAG,CAAEvB,QAAQ,iBACtB3C,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAK,IAAIpC,IAAI,CAACiC,QAAQ,CAAClC,IAAI,CAAC,CAAC0D,kBAAkB,CAAC,OAAO;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DpD,OAAA;gBAAA8C,QAAA,EAAKH,QAAQ,CAAC9B;cAAQ;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BpD,OAAA;gBAAA8C,QAAA,EAAKH,QAAQ,CAAC7B;cAAQ;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BpD,OAAA;gBAAA8C,QAAA,EAAKb,cAAc,CAACU,QAAQ,CAAC5B,SAAS;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7CpD,OAAA;gBAAIqC,KAAK,EAAE;kBAAE+B,UAAU,EAAE,MAAM;kBAAEpB,KAAK,EAAE;gBAAU,CAAE;gBAAAF,QAAA,EACjDb,cAAc,CAACU,QAAQ,CAACvB,UAAU;cAAC;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACLpD,OAAA;gBAAA8C,QAAA,EAAKH,QAAQ,CAAC3B;cAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5BpD,OAAA;gBAAA8C,QAAA,EAAKH,QAAQ,CAAC1B;cAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCpD,OAAA;gBAAA8C,QAAA,eACE9C,OAAA;kBAAQ6C,SAAS,EAAC,mBAAmB;kBAACR,KAAK,EAAE;oBAAEkB,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAO,CAAE;kBAAAvB,QAAA,EAAC;gBAExF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAdET,QAAQ,CAACxB,EAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAehB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAzRID,kBAAkB;AAAAqE,EAAA,GAAlBrE,kBAAkB;AA2RxB,eAAeA,kBAAkB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}