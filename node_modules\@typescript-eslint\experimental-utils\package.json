{"name": "@typescript-eslint/experimental-utils", "version": "2.34.0", "description": "(Experimental) Utilities for working with TypeScript + ESLint together", "keywords": ["eslint", "typescript", "estree"], "engines": {"node": "^8.10.0 || ^10.13.0 || >=11.10.1"}, "files": ["dist", "package.json", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/experimental-utils"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b tsconfig.build.json", "clean": "tsc -b tsconfig.build.json --clean", "format": "prettier --write \"./**/*.{ts,js,json,md}\" --ignore-path ../../.prettierignore", "lint": "eslint . --ext .js,.ts --ignore-path='../../.eslintignore'", "test": "jest --coverage", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@types/json-schema": "^7.0.3", "@typescript-eslint/typescript-estree": "2.34.0", "eslint-scope": "^5.0.0", "eslint-utils": "^2.0.0"}, "peerDependencies": {"eslint": "*"}, "devDependencies": {"typescript": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "gitHead": "f18890166146d8c6b8804ef705c04b15da269926"}