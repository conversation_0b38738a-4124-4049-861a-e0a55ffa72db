{"version": 3, "sources": ["filesize.es6.js"], "names": ["global", "filesize", "arg", "descriptor", "Math", "pow", "floor", "log", "e", "base", "bits", "ceil", "full", "fullforms", "locale", "localeOptions", "neg", "num", "output", "round", "unix", "separator", "spacer", "standard", "symbols", "result", "val", "isNaN", "TypeError", "fullform", "Array", "exponent", "symbol", "toFixed", "char<PERSON>t", "replace", "b", "test", "toLocaleString", "length", "toString", "value", "join", "iec", "bytes", "jedec", "partial", "opt", "exports", "define", "amd", "module", "window"], "mappings": "AAAA;;;;;;GAOC,UAAUA,CAAV,CAAkB,CAiBlB;;;;;;;IAQA,QAASC,EAAT,CAAmBC,CAAnB,CAAwBC,EAAa,EAArC,CAAyC,OAuDbC,KAAKC,GAvDQ,GAkCnCD,KAAKE,KAlC8B,GAkCxBF,KAAKG,GAlCmB,CACxC,GAECC,EAFD,CAEIC,CAFJ,CAEUC,CAFV,CAEgBC,CAFhB,CAEsBC,CAFtB,CAE4BC,CAF5B,CAEuCC,CAFvC,CAE+CC,CAF/C,CAE8DC,CAF9D,CAEmEC,CAFnE,CAEwEC,CAFxE,CAEgFC,CAFhF,CAEuFC,CAFvF,CAE6FC,CAF7F,CAEwGC,CAFxG,CAEgHC,CAFhH,CAE0HC,CAF1H,CAAIC,EAAS,EAAb,CACCC,EAAM,CADP,CAIA,GAAIC,MAAMzB,CAAN,CAAJ,CACC,KAAM,IAAI0B,UAAJ,CAAc,gBAAd,CAAN,CANuC,OASxClB,EAAO,OAAWA,IATsB,CAUxCU,EAAO,OAAWA,IAVsB,CAWxCX,EAAON,EAAWM,IAAX,EAAmB,CAXc,CAYxCU,EAA6B,IAAK,EAA1B,KAAWA,KAAX,CAAiDC,EAAO,CAAP,CAAW,CAA5D,CAA8BjB,EAAWgB,KAZT,CAaxCL,EAA+B,IAAK,EAA3B,KAAWA,MAAX,CAAmD,EAAnD,CAA+BX,EAAWW,MAbX,CAcxCC,EAAgBZ,EAAWY,aAAX,EAA4B,EAdJ,CAexCM,EAAqC,IAAK,EAA9B,KAAWA,SAAX,CAAyD,EAAzD,CAAkClB,EAAWkB,SAfjB,CAgBxCC,EAA+B,IAAK,EAA3B,KAAWA,MAAX,CAAmDF,EAAO,EAAP,CAAY,GAA/D,CAA+BjB,EAAWmB,MAhBX,CAiBxCE,EAAUrB,EAAWqB,OAAX,EAAsB,EAjBQ,CAkBxCD,EAAoB,CAAT,KAAapB,EAAWoB,QAAX,EAAuB,OAApC,CAA8C,OAlBjB,CAmBxCL,EAASf,EAAWe,MAAX,EAAqB,QAnBU,CAoBxCN,EAAO,OAAWiB,QApBsB,CAqBxChB,EAAYV,EAAWU,SAAX,WAAgCiB,MAAhC,CAAwC3B,EAAWU,SAAnD,CAA+D,EArBnC,CAsBxCL,EAA4B,IAAK,EAA7B,KAAWuB,QAAX,CAAuD,CAAC,CAAxD,CAAiC5B,EAAW4B,QAtBR,CAuBxCd,GAAaf,CAvB2B,CAwBxCc,EAAY,CAAN,EAxBkC,CAyBxCL,EAAc,CAAP,GAAW,GAAX,CAAkB,IAzBe,CA4BpCK,CA5BoC,GA6BvCC,EAAM,CAACA,CA7BgC,GAiC9B,CAAC,CAAP,MAAYU,MAAMnB,CAAN,CAjCwB,IAkCvCA,EAAI,EAAW,EAASS,CAAT,EAAgB,EAASN,CAAT,CAA3B,CAlCmC,CAoC/B,CAAJ,EApCmC,GAqCtCH,EAAI,CArCkC,GA0ChC,CAAJ,EA1CoC,GA2CvCA,EAAI,CA3CmC,EA8CzB,UAAX,IA9CoC,EA+ChCA,CA/CgC,EAmD5B,CAAR,IAnDoC,EAoDvCiB,EAAO,CAAP,EAAY,CApD2B,CAqDvCA,EAAO,CAAP,EAAYL,EAAO,EAAP,CAAYY,EAAOT,CAAP,EAAiBb,EAAO,MAAP,CAAgB,OAAjC,EAA0CF,CAA1C,CArDe,GAuDvCkB,EAAMT,GAAgB,CAAT,KAAa,EAAS,CAAT,CAAgB,EAAJ,EAAZ,CAAb,CAAmC,EAAS,GAAT,CAAeT,CAAf,CAA1C,CAvDiC,CAyDnCE,CAzDmC,GA0DtCgB,CA1DsC,EA0D1B,CA1D0B,CA4DlCA,GAAOf,CAAP,EAAmB,CAAJ,EA5DmB,GA6DrCe,CA7DqC,EA6DzBf,CA7DyB,CA8DrCH,GA9DqC,GAkEvCiB,EAAO,CAAP,GAAmBC,EAAIO,OAAJ,CAAgB,CAAJ,GAAQd,CAAR,CAAgB,CAA5B,CAlEoB,CAoEnCM,EAAO,CAAP,IAAcd,CAAd,EAA0B,CAAJ,EAAtB,EAAuD,IAAK,EAA7B,KAAWoB,QApEP,GAqEtCN,EAAO,CAAP,EAAY,CArE0B,CAsEtCjB,GAtEsC,EAyEvCiB,EAAO,CAAP,EAAqB,EAAT,MAAqB,CAAN,IAAf,CAAyBf,EAAO,IAAP,CAAc,IAAvC,CAA8CsB,EAAOT,CAAP,EAAiBb,EAAO,MAAP,CAAgB,OAAjC,EAA0CF,CAA1C,CAzEnB,CA2EnCY,CA3EmC,GA4EtCK,EAAO,CAAP,EAAyB,OAAb,KAAuBA,EAAO,CAAP,EAAUS,MAAV,CAAiB,CAAjB,CAAvB,CAAiD,CAAJ,GAAQT,EAAO,CAAP,EAAUU,OAAV,CAAkB,IAAlB,CAAwB,EAAxB,CAAR,CAAsCV,EAAO,CAAP,CA5EzD,CA8ElCW,EAAEC,IAAF,CAAOZ,EAAO,CAAP,CAAP,CA9EkC,GA+ErCA,EAAO,CAAP,EAAY,EAAWA,EAAO,CAAP,CAAX,CA/EyB,CAgFrCA,EAAO,CAAP,EAAY,EAhFyB,IAsFpCT,CAtFoC,GAuFvCS,EAAO,CAAP,EAAY,CAACA,EAAO,CAAP,CAvF0B,EA2FxCA,EAAO,CAAP,EAAYD,EAAQC,EAAO,CAAP,CAAR,GAAsBA,EAAO,CAAP,CA3FM,CA6FpC,MA7FoC,CA8FvCA,EAAO,CAAP,EAAYA,EAAO,CAAP,EAAUa,cAAV,EA9F2B,CA+Fb,CAAhB,GAAOC,MA/FsB,CAgGvCd,EAAO,CAAP,EAAYA,EAAO,CAAP,EAAUa,cAAV,CAAyBxB,CAAzB,CAAiCC,CAAjC,CAhG2B,CAiGV,CAAnB,GAAUwB,MAjGmB,GAkGvCd,EAAO,CAAP,EAAYA,EAAO,CAAP,EAAUe,QAAV,GAAqBL,OAArB,CAA6B,GAA7B,CAAkCd,CAAlC,CAlG2B,EAsGzB,OAAX,IAtGoC,EAuGhCI,CAvGgC,EA0GpCb,CA1GoC,GA2GvCa,EAAO,CAAP,EAAYZ,EAAUL,CAAV,EAAeK,EAAUL,CAAV,CAAf,CAA8BqB,EAASN,CAAT,EAAmBf,CAAnB,GAAyBE,EAAO,KAAP,CAAe,MAAxC,GAAiE,CAAd,KAAO,CAAP,EAAkB,EAAlB,CAAuB,GAA1E,CA3GH,EA8GzB,QAAX,IA9GoC,CA+GhC,CAAC+B,MAAOhB,EAAO,CAAP,CAAR,CAAmBO,OAAQP,EAAO,CAAP,CAA3B,CA/GgC,CAkHjCA,EAAOiB,IAAP,CAAYpB,CAAZ,CAlHiC,EAkDxC;AAmDA;AAcA,CAED;AA7IA,KAAMc,GAAI,SAAV,CACCJ,EAAS,CACRW,IAAK,CACJjC,KAAM,CAAC,GAAD,CAAM,KAAN,CAAa,KAAb,CAAoB,KAApB,CAA2B,KAA3B,CAAkC,KAAlC,CAAyC,KAAzC,CAAgD,KAAhD,CAAuD,KAAvD,CADF,CAEJkC,MAAO,CAAC,GAAD,CAAM,KAAN,CAAa,KAAb,CAAoB,KAApB,CAA2B,KAA3B,CAAkC,KAAlC,CAAyC,KAAzC,CAAgD,KAAhD,CAAuD,KAAvD,CAFH,CADG,CAKRC,MAAO,CACNnC,KAAM,CAAC,GAAD,CAAM,IAAN,CAAY,IAAZ,CAAkB,IAAlB,CAAwB,IAAxB,CAA8B,IAA9B,CAAoC,IAApC,CAA0C,IAA1C,CAAgD,IAAhD,CADA,CAENkC,MAAO,CAAC,GAAD,CAAM,IAAN,CAAY,IAAZ,CAAkB,IAAlB,CAAwB,IAAxB,CAA8B,IAA9B,CAAoC,IAApC,CAA0C,IAA1C,CAAgD,IAAhD,CAFD,CALC,CADV,CAWCf,EAAW,CACVc,IAAK,CAAC,EAAD,CAAK,MAAL,CAAa,MAAb,CAAqB,MAArB,CAA6B,MAA7B,CAAqC,MAArC,CAA6C,MAA7C,CAAqD,MAArD,CAA6D,MAA7D,CADK,CAEVE,MAAO,CAAC,EAAD,CAAK,MAAL,CAAa,MAAb,CAAqB,MAArB,CAA6B,MAA7B,CAAqC,MAArC,CAA6C,KAA7C,CAAoD,OAApD,CAA6D,OAA7D,CAFG,CAXZ,CA8IA5C,EAAS6C,OAAT,CAAmBC,GAAO7C,GAAOD,EAASC,CAAT,CAAc6C,CAAd,CA/If,CAkJK,WAAnB,QAAOC,QAlJO,CAoJW,UAAlB,QAAOC,OAAP,EAA+C,IAAK,EAApB,UAAOC,GApJhC,CAqJjBD,OAAO,IAAMhD,CAAb,CArJiB,CAuJjBD,EAAOC,QAAP,CAAkBA,CAvJD,CAmJjBkD,OAAOH,OAAP,CAAiB/C,CAMlB,CAzJA,EAyJmB,WAAlB,QAAOmD,OAAP,CAAyCpD,MAAzC,CAAgCoD,MAzJjC,C", "file": "unknown", "sourcesContent": ["/**\r\n * filesize\r\n *\r\n * @copyright 2019 <PERSON> <<EMAIL>>\r\n * @license BSD-3-Clause\r\n * @version 6.0.1\r\n */\r\n(function (global) {\r\n\tconst b = /^(b|B)$/,\r\n\t\tsymbol = {\r\n\t\t\tiec: {\r\n\t\t\t\tbits: [\"b\", \"Kib\", \"Mib\", \"Gib\", \"Tib\", \"Pib\", \"Eib\", \"Zib\", \"Yib\"],\r\n\t\t\t\tbytes: [\"B\", \"Ki<PERSON>\", \"MiB\", \"GiB\", \"TiB\", \"PiB\", \"EiB\", \"ZiB\", \"YiB\"]\r\n\t\t\t},\r\n\t\t\tjedec: {\r\n\t\t\t\tbits: [\"b\", \"Kb\", \"Mb\", \"Gb\", \"Tb\", \"Pb\", \"Eb\", \"Zb\", \"Yb\"],\r\n\t\t\t\tbytes: [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"]\r\n\t\t\t}\r\n\t\t},\r\n\t\tfullform = {\r\n\t\t\tiec: [\"\", \"kibi\", \"mebi\", \"gibi\", \"tebi\", \"pebi\", \"exbi\", \"zebi\", \"yobi\"],\r\n\t\t\tjedec: [\"\", \"kilo\", \"mega\", \"giga\", \"tera\", \"peta\", \"exa\", \"zetta\", \"yotta\"]\r\n\t\t};\r\n\r\n\t/**\r\n\t * filesize\r\n\t *\r\n\t * @method filesize\r\n\t * @param  {Mixed}   arg        String, Int or Float to transform\r\n\t * @param  {Object}  descriptor [Optional] Flags\r\n\t * @return {String}             Readable file size String\r\n\t */\r\n\tfunction filesize (arg, descriptor = {}) {\r\n\t\tlet result = [],\r\n\t\t\tval = 0,\r\n\t\t\te, base, bits, ceil, full, fullforms, locale, localeOptions, neg, num, output, round, unix, separator, spacer, standard, symbols;\r\n\r\n\t\tif (isNaN(arg)) {\r\n\t\t\tthrow new TypeError(\"Invalid number\");\r\n\t\t}\r\n\r\n\t\tbits = descriptor.bits === true;\r\n\t\tunix = descriptor.unix === true;\r\n\t\tbase = descriptor.base || 2;\r\n\t\tround = descriptor.round !== void 0 ? descriptor.round : unix ? 1 : 2;\r\n\t\tlocale = descriptor.locale !== void 0 ? descriptor.locale : \"\";\r\n\t\tlocaleOptions = descriptor.localeOptions || {};\r\n\t\tseparator = descriptor.separator !== void 0 ? descriptor.separator : \"\";\r\n\t\tspacer = descriptor.spacer !== void 0 ? descriptor.spacer : unix ? \"\" : \" \";\r\n\t\tsymbols = descriptor.symbols || {};\r\n\t\tstandard = base === 2 ? descriptor.standard || \"jedec\" : \"jedec\";\r\n\t\toutput = descriptor.output || \"string\";\r\n\t\tfull = descriptor.fullform === true;\r\n\t\tfullforms = descriptor.fullforms instanceof Array ? descriptor.fullforms : [];\r\n\t\te = descriptor.exponent !== void 0 ? descriptor.exponent : -1;\r\n\t\tnum = Number(arg);\r\n\t\tneg = num < 0;\r\n\t\tceil = base > 2 ? 1000 : 1024;\r\n\r\n\t\t// Flipping a negative number to determine the size\r\n\t\tif (neg) {\r\n\t\t\tnum = -num;\r\n\t\t}\r\n\r\n\t\t// Determining the exponent\r\n\t\tif (e === -1 || isNaN(e)) {\r\n\t\t\te = Math.floor(Math.log(num) / Math.log(ceil));\r\n\r\n\t\t\tif (e < 0) {\r\n\t\t\t\te = 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Exceeding supported length, time to reduce & multiply\r\n\t\tif (e > 8) {\r\n\t\t\te = 8;\r\n\t\t}\r\n\r\n\t\tif (output === \"exponent\") {\r\n\t\t\treturn e;\r\n\t\t}\r\n\r\n\t\t// Zero is now a special case because bytes divide by 1\r\n\t\tif (num === 0) {\r\n\t\t\tresult[0] = 0;\r\n\t\t\tresult[1] = unix ? \"\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\t\t} else {\r\n\t\t\tval = num / (base === 2 ? Math.pow(2, e * 10) : Math.pow(1000, e));\r\n\r\n\t\t\tif (bits) {\r\n\t\t\t\tval = val * 8;\r\n\r\n\t\t\t\tif (val >= ceil && e < 8) {\r\n\t\t\t\t\tval = val / ceil;\r\n\t\t\t\t\te++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tresult[0] = Number(val.toFixed(e > 0 ? round : 0));\r\n\r\n\t\t\tif (result[0] === ceil && e < 8 && descriptor.exponent === void 0) {\r\n\t\t\t\tresult[0] = 1;\r\n\t\t\t\te++;\r\n\t\t\t}\r\n\r\n\t\t\tresult[1] = base === 10 && e === 1 ? bits ? \"kb\" : \"kB\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\r\n\t\t\tif (unix) {\r\n\t\t\t\tresult[1] = standard === \"jedec\" ? result[1].charAt(0) : e > 0 ? result[1].replace(/B$/, \"\") : result[1];\r\n\r\n\t\t\t\tif (b.test(result[1])) {\r\n\t\t\t\t\tresult[0] = Math.floor(result[0]);\r\n\t\t\t\t\tresult[1] = \"\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Decorating a 'diff'\r\n\t\tif (neg) {\r\n\t\t\tresult[0] = -result[0];\r\n\t\t}\r\n\r\n\t\t// Applying custom symbol\r\n\t\tresult[1] = symbols[result[1]] || result[1];\r\n\r\n\t\tif (locale === true) {\r\n\t\t\tresult[0] = result[0].toLocaleString();\r\n\t\t} else if (locale.length > 0) {\r\n\t\t\tresult[0] = result[0].toLocaleString(locale, localeOptions);\r\n\t\t} else if (separator.length > 0) {\r\n\t\t\tresult[0] = result[0].toString().replace(\".\", separator);\r\n\t\t}\r\n\r\n\t\t// Returning Array, Object, or String (default)\r\n\t\tif (output === \"array\") {\r\n\t\t\treturn result;\r\n\t\t}\r\n\r\n\t\tif (full) {\r\n\t\t\tresult[1] = fullforms[e] ? fullforms[e] : fullform[standard][e] + (bits ? \"bit\" : \"byte\") + (result[0] === 1 ? \"\" : \"s\");\r\n\t\t}\r\n\r\n\t\tif (output === \"object\") {\r\n\t\t\treturn {value: result[0], symbol: result[1]};\r\n\t\t}\r\n\r\n\t\treturn result.join(spacer);\r\n\t}\r\n\r\n\t// Partial application for functional programming\r\n\tfilesize.partial = opt => arg => filesize(arg, opt);\r\n\r\n\t// CommonJS, AMD, script tag\r\n\tif (typeof exports !== \"undefined\") {\r\n\t\tmodule.exports = filesize;\r\n\t} else if (typeof define === \"function\" && define.amd !== void 0) {\r\n\t\tdefine(() => filesize);\r\n\t} else {\r\n\t\tglobal.filesize = filesize;\r\n\t}\r\n}(typeof window !== \"undefined\" ? window : global));\r\n"]}