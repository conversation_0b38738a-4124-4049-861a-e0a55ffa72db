{"version": 3, "file": "immer.umd.production.min.js", "sources": ["../src/utils/errors.ts", "../src/utils/common.ts", "../src/utils/plugins.ts", "../src/core/scope.ts", "../src/core/finalize.ts", "../src/core/proxy.ts", "../src/core/immerClass.ts", "../src/core/current.ts", "../src/plugins/es5.ts", "../src/plugins/patches.ts", "../src/plugins/mapset.ts", "../src/utils/env.ts", "../src/immer.ts", "../src/plugins/all.ts"], "sourcesContent": ["const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n"], "names": ["die", "error", "args", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "each", "obj", "iter", "enumerableOnly", "getArchtype", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "type_", "has", "prop", "prototype", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "base_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "getPlugin", "pluginKey", "plugin", "plugins", "loadPlugin", "implementation", "getCurrentScope", "currentScope", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "NOTHING", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "res", "assigned_", "concat", "autoFreeze_", "peek", "getDescriptorFromProto", "source", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "proxyMap_", "proxySet_", "isManual_", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "proxyProperty", "this", "mark<PERSON><PERSON>esSweep", "drafts", "hasArrayChanges", "hasObjectChanges", "baseValue", "baseIsDraft", "descriptor", "defineProperty", "mark<PERSON>hangesRecursively", "object", "min", "Math", "enablePatches", "deepClonePatchValue", "entries", "cloned", "immerable", "clonePatchValueIfNeeded", "ADD", "applyPatches_", "patches", "patch", "op", "parentType", "p", "type", "splice", "basePath", "inversePatches", "assignedValue", "origValue", "unshift", "replacement", "enableMapSet", "__extends", "d", "b", "__", "extendStatics", "prepareMapCopy", "prepareSetCopy", "assertUnrevoked", "JSON", "stringify", "setPrototypeOf", "__proto__", "DraftMap", "size", "cb", "thisArg", "_value", "_this", "values", "iterator", "iteratorSymbol", "_this2", "next", "r", "done", "_this3", "DraftSet", "hasSymbol", "Symbol", "hasProxies", "Reflect", "for", "getOwnPropertySymbols", "getOwnPropertyNames", "_desc$get", "currentState", "Number", "isNaN", "deleteProperty", "owner", "fn", "arguments", "apply", "Immer", "config", "recipe", "defaultBase", "self", "produce", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "then", "ip", "produceWithPatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "applyPatchesImpl", "bind"], "mappings": "+LA4CgBA,EAAIC,8BAA+BC,+BAAAA,0BAUxCC,oCACqBF,GAC7BC,EAAKE,OAAS,IAAMF,EAAKG,cAAIC,aAASA,SAAMC,KAAK,KAAO,iECvC3CC,EAAQC,WACdA,KAAWA,EAAMC,YAKXC,EAAYF,iBACtBA,aAawBA,OACxBA,GAA0B,iBAAVA,EAAoB,aACnCG,EAAQC,OAAOC,eAAeL,MACtB,OAAVG,eAGEG,EACLF,OAAOG,eAAeC,KAAKL,EAAO,gBAAkBA,EAAMM,mBAEvDH,IAASF,QAGG,mBAARE,GACPI,SAASC,SAASH,KAAKF,KAAUM,GAxBnBZ,IACda,MAAMC,QAAQd,MACZA,EAAMe,iBACNf,EAAMS,gCAANO,EAAoBD,KACtBE,EAAMjB,IACNkB,EAAMlB,aA0DQmB,EAAKC,EAAUC,EAAWC,YAAAA,IAAAA,UACrCC,EAAYH,IACbE,EAAiBlB,OAAOoB,KAAOC,GAASL,GAAKM,kBAAQC,GACjDL,GAAiC,iBAARK,GAAkBN,EAAKM,EAAKP,EAAIO,GAAMP,MAGrEA,EAAIM,kBAASE,EAAYC,UAAeR,EAAKQ,EAAOD,EAAOR,eAK7CG,EAAYO,OAErBC,EAAgCD,EAAM7B,UACrC8B,EACJA,EAAMC,EAAQ,EACbD,EAAMC,EAAQ,EACbD,EAAMC,EACRnB,MAAMC,QAAQgB,KAEdb,EAAMa,KAENZ,EAAMY,gBAMMG,EAAIH,EAAYI,cACxBX,EAAYO,GAChBA,EAAMG,IAAIC,GACV9B,OAAO+B,UAAU5B,eAAeC,KAAKsB,EAAOI,YAIhCE,EAAIN,EAA2BI,cAEvCX,EAAYO,GAA0BA,EAAMM,IAAIF,GAAQJ,EAAMI,YAItDG,EAAIP,EAAYQ,EAA6BtC,OACtDuC,EAAIhB,EAAYO,OAClBS,EAAoBT,EAAMO,IAAIC,EAAgBtC,OACzCuC,EACRT,EAAMU,IAAIxC,GACJ8B,EAAMQ,GAAkBtC,WAIhByC,EAAGC,EAAQC,UAEtBD,IAAMC,EACI,IAAND,GAAW,EAAIA,GAAM,EAAIC,EAEzBD,GAAMA,GAAKC,GAAMA,WAKV1B,EAAM2B,UACdC,GAAUD,aAAkBE,aAIpB5B,EAAM0B,UACdG,GAAUH,aAAkBI,aAGpBC,EAAOlB,UACfA,EAAMmB,GAASnB,EAAMoB,WAIbC,EAAYC,MACvBxC,MAAMC,QAAQuC,GAAO,OAAOxC,MAAMsB,UAAUmB,MAAM9C,KAAK6C,OACrDE,EAAcC,EAA0BH,UACvCE,EAAYtD,WACfuB,EAAOC,EAAQ8B,GACVE,EAAI,EAAGA,EAAIjC,EAAK7B,OAAQ8D,IAAK,KAC/B9B,EAAWH,EAAKiC,GAChBC,EAAOH,EAAY5B,QACrB+B,EAAKC,WACRD,EAAKC,YACLD,EAAKE,kBAKFF,EAAKtB,KAAOsB,EAAKrB,OACpBkB,EAAY5B,GAAO,CAClBiC,gBACAD,YACAE,WAAYH,EAAKG,WACjB7D,MAAOqD,EAAK1B,YAGRvB,OAAO0D,OAAO1D,OAAOC,eAAegD,GAAOE,YAWnCQ,EAAU3C,EAAU4C,mBAAAA,IAAAA,MAC/BC,EAAS7C,IAAQrB,EAAQqB,KAASlB,EAAYkB,KAC9CG,EAAYH,GAAO,IACtBA,EAAIiB,IAAMjB,EAAIoB,IAAMpB,EAAI8C,MAAQ9C,EAAI+C,OAASC,GAE9ChE,OAAO2D,OAAO3C,GACV4C,GAAM7C,EAAKC,YAAMO,EAAK3B,UAAU+D,EAAO/D,aALoBoB,EAShE,SAASgD,IACR7E,EAAI,YAGW0E,EAAS7C,UACb,MAAPA,GAA8B,iBAARA,GAEnBhB,OAAO6D,SAAS7C,YCxKRiD,EACfC,OAEMC,EAASC,EAAQF,UAClBC,GACJhF,EAAI,GAAI+E,GAGFC,WAGQE,EACfH,EACAI,GAEKF,EAAQF,KAAYE,EAAQF,GAAaI,YClC/BC,WAERC,WAkBQC,EACfC,EACAC,GAEIA,IACHV,EAAU,WACVS,EAAME,EAAW,GACjBF,EAAMG,EAAkB,GACxBH,EAAMI,EAAiBH,YAITI,EAAYL,GAC3BM,EAAWN,GACXA,EAAMO,EAAQ3D,QAAQ4D,GAEtBR,EAAMO,EAAU,cAGDD,EAAWN,GACtBA,IAAUF,IACbA,EAAeE,EAAMS,YAIPC,EAAWC,UAClBb,EArCD,CACNS,EAAS,GACTE,EAmCkCX,EAlClCc,EAkCgDD,EA/BhDE,KACAC,EAAoB,GAiCtB,SAASN,EAAYO,OACd9D,EAAoB8D,EAAM5F,OAE/B8B,EAAMC,OACND,EAAMC,EAEND,EAAM+D,IACF/D,EAAMgE,cC9DIC,EAAcC,EAAanB,GAC1CA,EAAMc,EAAqBd,EAAMO,EAAQ1F,WACnCuG,EAAYpB,EAAMO,EAAS,GAC3Bc,WAAaF,GAAwBA,IAAWC,SACjDpB,EAAMY,EAAOU,GACjB/B,EAAU,OAAOgC,EAAiBvB,EAAOmB,EAAQE,GAC9CA,GACCD,EAAUjG,GAAaqG,IAC1BnB,EAAYL,GACZvF,EAAI,IAEDW,EAAY+F,KAEfA,EAASM,EAASzB,EAAOmB,GACpBnB,EAAMS,GAASiB,EAAY1B,EAAOmB,IAEpCnB,EAAME,GACTX,EAAU,WAAWoC,EACpBP,EAAUjG,GAAakD,EACvB8C,EACAnB,EAAME,EACNF,EAAMG,IAKRgB,EAASM,EAASzB,EAAOoB,EAAW,IAErCf,EAAYL,GACRA,EAAME,GACTF,EAAMI,EAAgBJ,EAAME,EAAUF,EAAMG,GAEtCgB,IAAWS,EAAUT,SAG7B,SAASM,EAASI,EAAuB3G,EAAY4G,MAEhD3C,EAASjE,GAAQ,OAAOA,MAEtB+B,EAAoB/B,EAAMC,OAE3B8B,SACJZ,EACCnB,YACC2B,EAAKkF,UACLC,EAAiBH,EAAW5E,EAAO/B,EAAO2B,EAAKkF,EAAYD,SAGtD5G,KAGJ+B,EAAMgF,IAAWJ,EAAW,OAAO3G,MAElC+B,EAAMuE,SACVE,EAAYG,EAAW5E,EAAMoB,MACtBpB,EAAMoB,MAGTpB,EAAMiF,EAAY,CACtBjF,EAAMiF,KACNjF,EAAMgF,EAAOnB,QACPK,MAELlE,EAAMC,OAAiCD,EAAMC,EACzCD,EAAMmB,EAAQE,EAAYrB,EAAMkF,GACjClF,EAAMmB,EAKNgE,EAAajB,EACb/E,SACAa,EAAMC,IACTkF,EAAa,IAAIlE,IAAIiD,GACrBA,EAAO/B,QACPhD,MAEDC,EAAK+F,YAAavF,EAAKkF,UACtBC,EAAiBH,EAAW5E,EAAOkE,EAAQtE,EAAKkF,EAAYD,EAAM1F,MAGnEsF,EAAYG,EAAWV,MAEnBW,GAAQD,EAAU3B,GACrBX,EAAU,WAAW8C,EACpBpF,EACA6E,EACAD,EAAU3B,EACV2B,EAAU1B,UAINlD,EAAMmB,EAGd,SAAS4D,EACRH,EACAS,EACAC,EACAnF,EACA2E,EACAS,EACAC,MAGIxH,EAAQ8G,GAAa,KASlBW,EAAMjB,EAASI,EAAWE,EAP/BS,GACAF,OACAA,EAAapF,IACZC,EAAKmF,EAA8CK,EAAYvF,GAC7DoF,EAAUI,OAAOxF,cAIrBG,EAAIgF,EAAcnF,EAAMsF,IAGpBzH,EAAQyH,GAEL,OADNb,EAAUhB,UAED4B,GACVF,EAAa7E,IAAIqE,MAGd3G,EAAY2G,KAAgB5C,EAAS4C,GAAa,KAChDF,EAAUjB,EAAOiC,GAAehB,EAAUf,EAAqB,SAQpEW,EAASI,EAAWE,GAEfO,GAAgBA,EAAYL,EAAOxB,GACvCiB,EAAYG,EAAWE,IAI1B,SAASL,EAAY1B,EAAmB9E,EAAYgE,YAAAA,IAAAA,OAE9Cc,EAAMS,GAAWT,EAAMY,EAAOiC,GAAe7C,EAAMa,GACvD5B,EAAO/D,EAAOgE,GCqEhB,SAAS4D,EAAK/B,EAAgB3D,OACvBH,EAAQ8D,EAAM5F,UACL8B,EAAQkB,EAAOlB,GAAS8D,GACzB3D,GAcf,SAAS2F,EACRC,EACA5F,MAGMA,KAAQ4F,UACV3H,EAAQC,OAAOC,eAAeyH,GAC3B3H,GAAO,KACPuD,EAAOtD,OAAO2H,yBAAyB5H,EAAO+B,MAChDwB,EAAM,OAAOA,EACjBvD,EAAQC,OAAOC,eAAeF,aAKhB6H,EAAYjG,GACtBA,EAAMuE,IACVvE,EAAMuE,KACFvE,EAAMwD,GACTyC,EAAYjG,EAAMwD,aAKL0C,EAAYlG,GACtBA,EAAMmB,IACVnB,EAAMmB,EAAQE,EAAYrB,EAAMoB,aCtDlB+E,EACfzC,EACAzF,EACAmI,OAGMtC,EAAiB5E,EAAMjB,GAC1BqE,EAAU,UAAU+D,EAAUpI,EAAOmI,GACrCjH,EAAMlB,GACNqE,EAAU,UAAUgE,EAAUrI,EAAOmI,GACrC1C,EAAMW,WDvLT/C,EACA8E,OAEMrH,EAAUD,MAAMC,QAAQuC,GACxBtB,EAAoB,CACzBC,EAAOlB,IAAkC,EAEzCiG,EAAQoB,EAASA,EAAOpB,EAASpC,IAEjC2B,KAEAU,KAEAS,EAAW,GAEXlC,EAAS4C,EAEThF,EAAOE,EAEP4D,EAAQ,KAER/D,EAAO,KAEP4C,EAAS,KACTwC,MASG1F,EAAYb,EACZwG,EAA2CC,EAC3C1H,IACH8B,EAAS,CAACb,GACVwG,EAAQE,UAGeC,MAAMC,UAAU/F,EAAQ2F,GAAzCK,IAAAA,OAAQC,IAAAA,aACf9G,EAAMkF,EAAS4B,EACf9G,EAAM+D,EAAU8C,EACTC,GC6Ia7I,EAAOmI,GACxB9D,EAAU,OAAOyE,EAAgB9I,EAAOmI,UAE7BA,EAASA,EAAOpB,EAASpC,KACjCU,EAAQ0D,KAAKlD,GACZA,WC9NQmD,EAAQhJ,UAClBD,EAAQC,IAAQT,EAAI,GAAIS,GAI9B,SAASiJ,EAAYjJ,OACfE,EAAYF,GAAQ,OAAOA,MAE5BkJ,EADEnH,EAAgC/B,EAAMC,GAEtCkJ,EAAW5H,EAAYvB,MACzB+B,EAAO,KAERA,EAAMuE,IACNvE,EAAMC,EAAQ,IAAMqC,EAAU,OAAO+E,EAAYrH,IAElD,OAAOA,EAAMoB,EAEdpB,EAAMiF,KACNkC,EAAOG,EAAWrJ,EAAOmJ,GACzBpH,EAAMiF,UAENkC,EAAOG,EAAWrJ,EAAOmJ,UAG1BhI,EAAK+H,YAAOvH,EAAKkF,GACZ9E,GAASK,EAAIL,EAAMoB,EAAOxB,KAASkF,GACvCxE,EAAI6G,EAAMvH,EAAKsH,EAAYpC,WAGrBsC,EAA4B,IAAInG,IAAIkG,GAAQA,EAxBpD,CAHoBlJ,GA8BpB,SAASqJ,EAAWrJ,EAAYmJ,UAEvBA,iBAEC,IAAIrG,IAAI9C,iBAGRa,MAAMyI,KAAKtJ,UAEboD,EAAYpD,YClCJuJ,aA8ENC,EACRtH,EACA2B,OAEIH,EAAOH,EAAYrB,UACnBwB,EACHA,EAAKG,WAAaA,EAElBN,EAAYrB,GAAQwB,EAAO,CAC1BE,gBACAC,WAAAA,EACAzB,sBAIQoG,EAAYpG,IAHLqH,KAAKxJ,GAGWiC,IAE/BG,aAAerC,GAIdwI,EAAYnG,IAHEoH,KAAKxJ,GAGIiC,EAAMlC,KAIzB0D,WAICgG,EAAiBC,OAKpB,IAAIlG,EAAIkG,EAAOhK,OAAS,EAAG8D,GAAK,EAAGA,IAAK,KACtC1B,EAAkB4H,EAAOlG,GAAGxD,OAC7B8B,EAAMuE,SACFvE,EAAMC,UAER4H,EAAgB7H,IAAQiG,EAAYjG,gBAGpC8H,EAAiB9H,IAAQiG,EAAYjG,cA6DrC8H,EAAiB9H,WAClBoB,EAAiBpB,EAAjBoB,EAAO8D,EAAUlF,EAAVkF,EAIRzF,EAAOC,EAAQwF,GACZxD,EAAIjC,EAAK7B,OAAS,EAAG8D,GAAK,EAAGA,IAAK,KACpC9B,EAAWH,EAAKiC,MAClB9B,IAAQ1B,OACN6J,EAAY3G,EAAMxB,eAEpBmI,IAA4B7H,EAAIkB,EAAOxB,gBAMpC3B,EAAQiH,EAAOtF,GACfI,EAAoB/B,GAASA,EAAMC,MACrC8B,EAAQA,EAAMoB,IAAU2G,GAAarH,EAAGzC,EAAO8J,iBAQ/CC,IAAgB5G,EAAMlD,UACrBuB,EAAK7B,SAAW8B,EAAQ0B,GAAOxD,QAAUoK,EAAc,EAAI,YAG1DH,EAAgB7H,OACjBkF,EAAUlF,EAAVkF,KACHA,EAAOtH,SAAWoC,EAAMoB,EAAMxD,OAAQ,aASpCqK,EAAa5J,OAAO2H,yBACzBd,EACAA,EAAOtH,OAAS,MAGbqK,IAAeA,EAAW5H,IAAK,aAE9B,IAAIqB,EAAI,EAAGA,EAAIwD,EAAOtH,OAAQ8D,QAC7BwD,EAAO1G,eAAekD,GAAI,sBA3J3BF,EAAoD,GA2K1DkB,EAAW,MAAO,CACjBqE,WA5MAzF,EACA8E,OAEMrH,EAAUD,MAAMC,QAAQuC,GACxBwC,WA1BiB/E,EAAkBuC,MACrCvC,EAAS,SACN+E,EAAYhF,MAAMwC,EAAK1D,QACpB8D,EAAI,EAAGA,EAAIJ,EAAK1D,OAAQ8D,IAChCrD,OAAO6J,eAAepE,EAAO,GAAKpC,EAAG+F,EAAc/F,cAC7CoC,MAEDtC,EAAcC,EAA0BH,UACvCE,EAAYtD,WACbuB,EAAOC,EAAQ8B,GACZE,EAAI,EAAGA,EAAIjC,EAAK7B,OAAQ8D,IAAK,KAC/B9B,EAAWH,EAAKiC,GACtBF,EAAY5B,GAAO6H,EAClB7H,EACAb,KAAayC,EAAY5B,GAAKkC,mBAGzBzD,OAAO0D,OAAO1D,OAAOC,eAAegD,GAAOE,IAStBzC,EAASuC,GAEhCtB,EAAwC,CAC7CC,EAAOlB,IAAgC,EACvCiG,EAAQoB,EAASA,EAAOpB,EAASpC,IACjC2B,KACAU,KACAS,EAAW,GACXlC,EAAS4C,EAEThF,EAAOE,EAEP4D,EAAQpB,EACR3C,EAAO,KACP6C,KACAuC,aAGDlI,OAAO6J,eAAepE,EAAO5F,EAAa,CACzCD,MAAO+B,EAEP4B,cAEMkC,GAkLPQ,WAvPAvB,EACAmB,EACAE,GAEKA,EASJpG,EAAQkG,IACPA,EAAOhG,GAA0B8G,IAAWjC,GAE7C4E,EAAiB5E,EAAMO,IAXnBP,EAAME,YAwHHkF,EAAuBC,MAC1BA,GAA4B,iBAAXA,OAChBpI,EAA8BoI,EAAOlK,MACtC8B,OACEoB,EAAmCpB,EAAnCoB,EAAO8D,EAA4BlF,EAA5BkF,EAAQQ,EAAoB1F,EAApB0F,EAAWzF,EAASD,EAATC,SAC7BA,EAKHb,EAAK8F,YAAQtF,GACPA,IAAgB1B,aAEhBkD,EAAcxB,IAAuBM,EAAIkB,EAAOxB,GAGzC8F,EAAU9F,IAErBuI,EAAuBjD,EAAOtF,KAJ9B8F,EAAU9F,MACVqG,EAAYjG,QAOdZ,EAAKgC,YAAOxB,YAEPsF,EAAOtF,IAAuBM,EAAIgF,EAAQtF,KAC7C8F,EAAU9F,MACVqG,EAAYjG,YAGR,OAAIC,EAA8B,IACpC4H,EAAgB7H,KACnBiG,EAAYjG,GACZ0F,EAAU9H,WAGPsH,EAAOtH,OAASwD,EAAMxD,WACpB,IAAI8D,EAAIwD,EAAOtH,OAAQ8D,EAAIN,EAAMxD,OAAQ8D,IAAKgE,EAAUhE,eAExD,IAAIA,EAAIN,EAAMxD,OAAQ8D,EAAIwD,EAAOtH,OAAQ8D,IAAKgE,EAAUhE,cAIxD2G,EAAMC,KAAKD,IAAInD,EAAOtH,OAAQwD,EAAMxD,QAEjC8D,EAAI,EAAGA,EAAI2G,EAAK3G,IAEnBwD,EAAO1G,eAAekD,KAC1BgE,EAAUhE,gBAEPgE,EAAUhE,IAAkByG,EAAuBjD,EAAOxD,QAxKvCqB,EAAMO,EAAS,IAGvCqE,EAAiB5E,EAAMO,KA+OxB+D,WAboBrH,cACbA,EAAMC,EACV6H,EAAiB9H,GACjB6H,EAAgB7H,eC9OLuI,aA6PNC,EAAoBnJ,OACvBlB,EAAYkB,GAAM,OAAOA,KAC1BP,MAAMC,QAAQM,GAAM,OAAOA,EAAIxB,IAAI2K,MACnCtJ,EAAMG,GACT,OAAO,IAAI0B,IACVjC,MAAMyI,KAAKlI,EAAIoJ,WAAW5K,uBAAgB,MAAI2K,gBAE5CrJ,EAAME,GAAM,OAAO,IAAI4B,IAAInC,MAAMyI,KAAKlI,GAAKxB,IAAI2K,QAC7CE,EAASrK,OAAO0D,OAAO1D,OAAOC,eAAee,QAC9C,IAAMO,KAAOP,EAAKqJ,EAAO9I,GAAO4I,EAAoBnJ,EAAIO,WACzDM,EAAIb,EAAKsJ,KAAYD,EAAOC,GAAatJ,EAAIsJ,IAC1CD,WAGCE,EAA2BvJ,UAC/BrB,EAAQqB,GACJmJ,EAAoBnJ,GACdA,MA5QTwJ,EAAM,MA+QZnG,EAAW,UAAW,CACrBoG,WAlGyBhF,EAAUiF,UACnCA,EAAQpJ,kBAAQqJ,WACRnE,EAAYmE,EAAZnE,KAAMoE,EAAMD,EAANC,GAET3H,EAAYwC,EACPpC,EAAI,EAAGA,EAAImD,EAAKjH,OAAS,EAAG8D,IAAK,KACnCwH,EAAa1J,EAAY8B,GAC3B6H,EAAItE,EAAKnD,GACI,iBAANyH,GAA+B,iBAANA,IACnCA,EAAI,GAAKA,OAKRD,OAAkCA,GAC5B,cAANC,GAA2B,gBAANA,GAEtB3L,EAAI,IACe,mBAAT8D,GAA6B,cAAN6H,GAAmB3L,EAAI,IAErC,iBADpB8D,EAAOjB,EAAIiB,EAAM6H,KACa3L,EAAI,GAAIqH,EAAK9G,KAAK,UAG3CqL,EAAO5J,EAAY8B,GACnBrD,EAAQuK,EAAoBQ,EAAM/K,OAClC2B,EAAMiF,EAAKA,EAAKjH,OAAS,UACvBqL,OAzMM,iBA2MJG,iBAEC9H,EAAKhB,IAAIV,EAAK3B,UAGrBT,EAAI,mBAMI8D,EAAK1B,GAAO3B,OAElB4K,SACIO,gBAES,MAARxJ,EACJ0B,EAAK0F,KAAK/I,GACVqD,EAAK+H,OAAOzJ,EAAY,EAAG3B,iBAEvBqD,EAAKhB,IAAIV,EAAK3B,iBAEdqD,EAAKb,IAAIxC,kBAERqD,EAAK1B,GAAO3B,MAjOX,gBAoOHmL,iBAEC9H,EAAK+H,OAAOzJ,EAAY,iBAExB0B,EAAKc,OAAOxC,iBAEZ0B,EAAKc,OAAO4G,EAAM/K,6BAEXqD,EAAK1B,WAGrBpC,EAAI,GAAIyL,OAIJnF,GA6BPsB,WA7QApF,EACAsJ,EACAP,EACAQ,UAEQvJ,EAAMC,wCAgFdD,EACAsJ,EACAP,EACAQ,OAEOnI,EAAgBpB,EAAhBoB,EAAOD,EAASnB,EAATmB,EACd/B,EAAKY,EAAM0F,YAAa9F,EAAK4J,OACtBC,EAAYpJ,EAAIe,EAAOxB,GACvB3B,EAAQoC,EAAIc,EAAQvB,GACpBqJ,EAAMO,EAAyBtJ,EAAIkB,EAAOxB,GAnGlC,UAmGmDiJ,EAjGpD,YAkGTY,IAAcxL,GApGJ,YAoGagL,OACrBpE,EAAOyE,EAAS3D,OAAO/F,GAC7BmJ,EAAQ/B,KApGK,WAoGAiC,EAAgB,CAACA,GAAAA,EAAIpE,KAAAA,GAAQ,CAACoE,GAAAA,EAAIpE,KAAAA,EAAM5G,MAAAA,IACrDsL,EAAevC,KACdiC,IAAOJ,EACJ,CAACI,GAvGQ,SAuGIpE,KAAAA,GAvGJ,WAwGToE,EACA,CAACA,GAAIJ,EAAKhE,KAAAA,EAAM5G,MAAO2K,EAAwBa,IAC/C,CAACR,GA5GS,UA4GIpE,KAAAA,EAAM5G,MAAO2K,EAAwBa,UA7FrDzJ,EACAsJ,EACAP,EACAQ,iCAgBHvJ,EACAsJ,EACAP,EACAQ,OAEKnI,EAAoBpB,EAApBoB,EAAOsE,EAAa1F,EAAb0F,EACRvE,EAAQnB,EAAMmB,KAGdA,EAAMvD,OAASwD,EAAMxD,OAAQ,OAEd,CAACuD,EAAOC,GAAxBA,OAAOD,aACoB,CAACoI,EAAgBR,GAA5CA,OAASQ,WAIP,IAAI7H,EAAI,EAAGA,EAAIN,EAAMxD,OAAQ8D,OAC7BgE,EAAUhE,IAAMP,EAAMO,KAAON,EAAMM,GAAI,KACpCmD,EAAOyE,EAAS3D,OAAO,CAACjE,IAC9BqH,EAAQ/B,KAAK,CACZiC,GAtDY,UAuDZpE,KAAAA,EAGA5G,MAAO2K,EAAwBzH,EAAMO,MAEtC6H,EAAevC,KAAK,CACnBiC,GA7DY,UA8DZpE,KAAAA,EACA5G,MAAO2K,EAAwBxH,EAAMM,UAMnC,IAAIA,EAAIN,EAAMxD,OAAQ8D,EAAIP,EAAMvD,OAAQ8D,IAAK,KAC3CmD,EAAOyE,EAAS3D,OAAO,CAACjE,IAC9BqH,EAAQ/B,KAAK,CACZiC,GAAIJ,EACJhE,KAAAA,EAGA5G,MAAO2K,EAAwBzH,EAAMO,MAGnCN,EAAMxD,OAASuD,EAAMvD,QACxB2L,EAAevC,KAAK,CACnBiC,GAjFa,UAkFbpE,KAAMyE,EAAS3D,OAAO,CAAC,WACvB1H,MAAOmD,EAAMxD,UA7DeoC,EAAOsJ,EAAUP,EAASQ,0BA4FxDvJ,EACAsJ,EACAP,EACAQ,OAEKnI,EAAgBpB,EAAhBoB,EAAOD,EAASnB,EAATmB,EAERO,EAAI,EACRN,EAAMzB,kBAAS1B,OACTkD,EAAOjB,IAAIjC,GAAQ,KACjB4G,EAAOyE,EAAS3D,OAAO,CAACjE,IAC9BqH,EAAQ/B,KAAK,CACZiC,GA5HW,SA6HXpE,KAAAA,EACA5G,MAAAA,IAEDsL,EAAeG,QAAQ,CACtBT,GAAIJ,EACJhE,KAAAA,EACA5G,MAAAA,IAGFyD,OAEDA,EAAI,EACJP,EAAOxB,kBAAS1B,OACVmD,EAAMlB,IAAIjC,GAAQ,KAChB4G,EAAOyE,EAAS3D,OAAO,CAACjE,IAC9BqH,EAAQ/B,KAAK,CACZiC,GAAIJ,EACJhE,KAAAA,EACA5G,MAAAA,IAEDsL,EAAeG,QAAQ,CACtBT,GAlJW,SAmJXpE,KAAAA,EACA5G,MAAAA,IAGFyD,QAhIG1B,EACDsJ,EACAP,EACAQ,KAuPH7E,WArHAqD,EACA4B,EACAZ,EACAQ,GAEAR,EAAQ/B,KAAK,CACZiC,GApKc,UAqKdpE,KAAM,GACN5G,MAAO0L,IAAgBhF,SAAsBgF,IAE9CJ,EAAevC,KAAK,CACnBiC,GAzKc,UA0KdpE,KAAM,GACN5G,MAAO8J,gBClLM6B,aAgBNC,EAAUC,EAAQC,YAEjBC,SACHtL,YAAcoL,EAFpBG,EAAcH,EAAGC,GAIjBD,EAAE1J,WAEC4J,EAAG5J,UAAY2J,EAAE3J,UAAY,IAAI4J,YA8J5BE,EAAelK,GAClBA,EAAMmB,IACVnB,EAAM0F,EAAY,IAAI3E,IACtBf,EAAMmB,EAAQ,IAAIJ,IAAIf,EAAMoB,aA0HrB+I,EAAenK,GAClBA,EAAMmB,IAEVnB,EAAMmB,EAAQ,IAAIF,IAClBjB,EAAMoB,EAAMzB,kBAAQ1B,MACfE,EAAYF,GAAQ,KACjB6F,EAAQqC,EAAYnG,EAAMgF,EAAOrB,EAAQ1F,EAAO+B,GACtDA,EAAMsD,EAAQhD,IAAIrC,EAAO6F,GACzB9D,EAAMmB,EAAOV,IAAIqD,QAEjB9D,EAAMmB,EAAOV,IAAIxC,gBAMZmM,EAAgBpK,GACpBA,EAAMgE,GAAUxG,EAAI,EAAG6M,KAAKC,UAAUpJ,EAAOlB,SAjU9CiK,EAAgB,SAASH,EAAQC,UACpCE,EACC5L,OAAOkM,gBACN,CAACC,UAAW,cAAe1L,OAC3B,SAASgL,EAAGC,GACXD,EAAEU,UAAYT,IAEhB,SAASD,EAAGC,OACN,IAAIZ,KAAKY,EAAOA,EAAEvL,eAAe2K,KAAIW,EAAEX,GAAKY,EAAEZ,MAEhCW,EAAGC,IAcnBU,EAAY,oBAGRA,EAAoB5J,EAAgBuF,eACvClI,GAAe,CACnB+B,IACAuD,EAAS4C,EACTpB,EAAQoB,EAASA,EAAOpB,EAASpC,IACjC2B,KACAU,KACA9D,SACAuE,SACAtE,EAAOP,EACPqE,EAAQwC,KACRnB,KACAvC,MAEM0D,KAhBRmC,EAAUY,EAmJR1J,SAjIIoI,EAAIsB,EAASrK,iBAEnB/B,OAAO6J,eAAeiB,EAAG,OAAQ,CAChC9I,IAAK,kBACGa,EAAOwG,KAAKxJ,IAAcwM,QAMnCvB,EAAEjJ,IAAM,SAASN,UACTsB,EAAOwG,KAAKxJ,IAAcgC,IAAIN,IAGtCuJ,EAAE7I,IAAM,SAASV,EAAU3B,OACpB+B,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GACXkB,EAAOlB,GAAOE,IAAIN,IAAQsB,EAAOlB,GAAOK,IAAIT,KAAS3B,IACzDiM,EAAelK,GACfiG,EAAYjG,GACZA,EAAM0F,EAAWpF,IAAIV,MACrBI,EAAMmB,EAAOb,IAAIV,EAAK3B,GACtB+B,EAAM0F,EAAWpF,IAAIV,OAEf8H,MAGRyB,EAAE/G,OAAS,SAASxC,OACd8H,KAAKxH,IAAIN,gBAIRI,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GAChBkK,EAAelK,GACfiG,EAAYjG,GACRA,EAAMoB,EAAMlB,IAAIN,GACnBI,EAAM0F,EAAWpF,IAAIV,MAErBI,EAAM0F,EAAWtD,OAAOxC,GAEzBI,EAAMmB,EAAOiB,OAAOxC,OAIrBuJ,EAAEhH,MAAQ,eACHnC,EAAkB0H,KAAKxJ,GAC7BkM,EAAgBpK,GACZkB,EAAOlB,GAAO0K,OACjBR,EAAelK,GACfiG,EAAYjG,GACZA,EAAM0F,EAAY,IAAI3E,IACtB3B,EAAKY,EAAMoB,YAAOxB,GACjBI,EAAM0F,EAAWpF,IAAIV,SAEtBI,EAAMmB,EAAOgB,UAIfgH,EAAExJ,QAAU,SACXgL,EACAC,cAGA1J,EADwBwG,KAAKxJ,IACfyB,kBAASkL,EAAajL,GACnC+K,EAAGlM,KAAKmM,EAASE,EAAKzK,IAAIT,GAAMA,EAAKkL,OAIvC3B,EAAE9I,IAAM,SAAST,OACVI,EAAkB0H,KAAKxJ,GAC7BkM,EAAgBpK,OACV/B,EAAQiD,EAAOlB,GAAOK,IAAIT,MAC5BI,EAAMiF,IAAe9G,EAAYF,UAC7BA,KAEJA,IAAU+B,EAAMoB,EAAMf,IAAIT,UACtB3B,MAGF6F,EAAQqC,EAAYnG,EAAMgF,EAAOrB,EAAQ1F,EAAO+B,UACtDkK,EAAelK,GACfA,EAAMmB,EAAOb,IAAIV,EAAKkE,GACfA,GAGRqF,EAAE1J,KAAO,kBACDyB,EAAOwG,KAAKxJ,IAAcuB,QAGlC0J,EAAE4B,OAAS,wBACJC,EAAWtD,KAAKjI,oBAEpBwL,GAAiB,kBAAMC,EAAKH,YAC7BI,KAAM,eACCC,EAAIJ,EAASG,cAEfC,EAAEC,KAAaD,EAEZ,CACNC,QACApN,MAHaiN,EAAK7K,IAAI+K,EAAEnN,YAS5BkL,EAAEV,QAAU,wBACLuC,EAAWtD,KAAKjI,oBAEpBwL,GAAiB,kBAAMK,EAAK7C,aAC7B0C,KAAM,eACCC,EAAIJ,EAASG,UAEfC,EAAEC,KAAM,OAAOD,MACbnN,EAAQqN,EAAKjL,IAAI+K,EAAEnN,aAClB,CACNoN,QACApN,MAAO,CAACmN,EAAEnN,MAAOA,QAMrBkL,EAAE8B,GAAkB,kBACZvD,KAAKe,WAGNgC,EAnJU,GAkKZc,EAAY,oBAGRA,EAAoB1K,EAAgBuF,eACvClI,GAAe,CACnB+B,IACAuD,EAAS4C,EACTpB,EAAQoB,EAASA,EAAOpB,EAASpC,IACjC2B,KACAU,KACA9D,SACAC,EAAOP,EACPqE,EAAQwC,KACRpE,EAAS,IAAIvC,IACbiD,KACAuC,MAEMmB,KAhBRmC,EAAU0B,EA8GRtK,SA5FIkI,EAAIoC,EAASnL,iBAEnB/B,OAAO6J,eAAeiB,EAAG,OAAQ,CAChC9I,IAAK,kBACGa,EAAOwG,KAAKxJ,IAAcwM,QAKnCvB,EAAEjJ,IAAM,SAASjC,OACV+B,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GAEXA,EAAMmB,IAGPnB,EAAMmB,EAAMjB,IAAIjC,OAChB+B,EAAMsD,EAAQpD,IAAIjC,KAAU+B,EAAMmB,EAAMjB,IAAIF,EAAMsD,EAAQjD,IAAIpC,KAH1D+B,EAAMoB,EAAMlB,IAAIjC,IAQzBkL,EAAE1I,IAAM,SAASxC,OACV+B,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GACX0H,KAAKxH,IAAIjC,KACbkM,EAAenK,GACfiG,EAAYjG,GACZA,EAAMmB,EAAOV,IAAIxC,IAEXyJ,MAGRyB,EAAE/G,OAAS,SAASnE,OACdyJ,KAAKxH,IAAIjC,gBAIR+B,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GAChBmK,EAAenK,GACfiG,EAAYjG,GAEXA,EAAMmB,EAAOiB,OAAOnE,MACnB+B,EAAMsD,EAAQpD,IAAIjC,IAChB+B,EAAMmB,EAAOiB,OAAOpC,EAAMsD,EAAQjD,IAAIpC,KAK3CkL,EAAEhH,MAAQ,eACHnC,EAAkB0H,KAAKxJ,GAC7BkM,EAAgBpK,GACZkB,EAAOlB,GAAO0K,OACjBP,EAAenK,GACfiG,EAAYjG,GACZA,EAAMmB,EAAOgB,UAIfgH,EAAE4B,OAAS,eACJ/K,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GAChBmK,EAAenK,GACRA,EAAMmB,EAAO4J,UAGrB5B,EAAEV,QAAU,eACLzI,EAAkB0H,KAAKxJ,UAC7BkM,EAAgBpK,GAChBmK,EAAenK,GACRA,EAAMmB,EAAOsH,WAGrBU,EAAE1J,KAAO,kBACDiI,KAAKqD,UAGb5B,EAAE8B,GAAkB,kBACZvD,KAAKqD,UAGb5B,EAAExJ,QAAU,SAAiBgL,EAASC,WAC/BI,EAAWtD,KAAKqD,SAClB7G,EAAS8G,EAASG,QACdjH,EAAOmH,MACdV,EAAGlM,KAAKmM,EAAS1G,EAAOjG,MAAOiG,EAAOjG,MAAOyJ,MAC7CxD,EAAS8G,EAASG,QAIbI,EA9GU,GA0IlB7I,EAAW,SAAU,CAAC2D,WAtJexF,EAAWuF,UAExC,IAAIqE,EAAS5J,EAAQuF,IAoJIE,WAzBIzF,EAAWuF,UAExC,IAAImF,EAAS1K,EAAQuF,YP1S1BvD,EQpBE2I,EACa,oBAAXC,QAAiD,iBAAhBA,OAAO,KACnC3K,EAAwB,oBAARC,IAChBC,EAAwB,oBAARC,IAChByK,EACK,oBAAV/E,gBACAA,MAAMC,WACM,oBAAZ+E,QAKKhH,EAAmB6G,EAC7BC,OAAOG,IAAI,yBACR,uBAUO5M,EAA2BwM,EACrCC,OAAOG,IAAI,mBACV,qBAES1N,EAA6BsN,EACvCC,OAAOG,IAAI,eACV,iBAGSX,EACM,oBAAVQ,QAAyBA,OAAOT,UAAc,aVJjDnM,EAAmBR,GAAAA,OAAO+B,UAAU1B,YA4B7BgB,EACO,oBAAZiM,SAA2BA,QAAQjM,QACvCiM,QAAQjM,iBACDrB,OAAOwN,sBACd,SAAAxM,UACAhB,OAAOyN,oBAAoBzM,GAAKsG,OAC/BtH,OAAOwN,sBAAsBxM,KAEHhB,OAAOyN,oBAEzBrK,EACZpD,OAAOoD,2BACP,SAAmCZ,OAE5B4E,EAAW,UACjB/F,EAAQmB,GAAQlB,kBAAQC,GACvB6F,EAAI7F,GAAOvB,OAAO2H,yBAAyBnF,EAAQjB,MAE7C6F,GCnEHhD,EA4BF,GGyDSgE,EAAwC,CACpDpG,aAAIL,EAAOG,MACNA,IAASjC,EAAa,OAAO8B,MAE3B+F,EAAS7E,EAAOlB,OACjBE,EAAI6F,EAAQ5F,UAwInB,SAA2BH,EAAmB+F,EAAa5F,SACpDwB,EAAOmE,EAAuBC,EAAQ5F,UACrCwB,EACJ,UAAWA,EACVA,EAAK1D,gBAGL0D,EAAKtB,wBAAL0L,EAAUtN,KAAKuB,EAAMkF,UAP1B,CAtI4BlF,EAAO+F,EAAQ5F,OAEnClC,EAAQ8H,EAAO5F,UACjBH,EAAMiF,IAAe9G,EAAYF,GAC7BA,EAIJA,IAAU4H,EAAK7F,EAAMoB,EAAOjB,IAC/B+F,EAAYlG,GACJA,EAAMmB,EAAOhB,GAAegG,EACnCnG,EAAMgF,EAAOrB,EACb1F,EACA+B,IAGK/B,GAERiC,aAAIF,EAAOG,UACHA,KAAQe,EAAOlB,IAEvBN,iBAAQM,UACA2L,QAAQjM,QAAQwB,EAAOlB,KAE/BM,aACCN,EACAG,EACAlC,OAEM0D,EAAOmE,EAAuB5E,EAAOlB,GAAQG,MAC/CwB,MAAAA,SAAAA,EAAMrB,WAGTqB,EAAKrB,IAAI7B,KAAKuB,EAAMkF,EAAQjH,UAGxB+B,EAAMuE,EAAW,KAGf0C,EAAUpB,EAAK3E,EAAOlB,GAAQG,GAE9B6L,EAAiC/E,MAAAA,SAAAA,EAAU/I,MAC7C8N,GAAgBA,EAAa5K,IAAUnD,SAC1C+B,EAAMmB,EAAOhB,GAAQlC,EACrB+B,EAAM0F,EAAUvF,YAGbO,EAAGzC,EAAOgJ,cAAahJ,GAAuBiC,EAAIF,EAAMoB,EAAOjB,IAClE,SACD+F,EAAYlG,GACZiG,EAAYjG,UAIXA,EAAMmB,EAAOhB,KAAUlC,aAEtBA,GAAuBkC,KAAQH,EAAMmB,IAEtC8K,OAAOC,MAAMjO,IAAUgO,OAAOC,MAAMlM,EAAMmB,EAAOhB,MAKnDH,EAAMmB,EAAOhB,GAAQlC,EACrB+B,EAAM0F,EAAUvF,WAGjBgM,wBAAenM,EAAOG,mBAEjB0F,EAAK7F,EAAMoB,EAAOjB,IAAuBA,KAAQH,EAAMoB,GAC1DpB,EAAM0F,EAAUvF,MAChB+F,EAAYlG,GACZiG,EAAYjG,WAGLA,EAAM0F,EAAUvF,GAGpBH,EAAMmB,UAAcnB,EAAMmB,EAAMhB,OAKrC6F,kCAAyBhG,EAAOG,OACzBiM,EAAQlL,EAAOlB,GACf2B,EAAOgK,QAAQ3F,yBAAyBoG,EAAOjM,UAChDwB,EACE,CACNC,YACAC,iBAAc7B,EAAMC,GAA2C,WAATE,EACtD2B,WAAYH,EAAKG,WACjB7D,MAAOmO,EAAMjM,IALIwB,GAQnBuG,0BACC1K,EAAI,KAELc,wBAAe0B,UACP3B,OAAOC,eAAe0B,EAAMoB,IAEpCmJ,0BACC/M,EAAI,MAQAkJ,GAA8C,GACpDtH,EAAKqH,YAAc7G,EAAKyM,GAEvB3F,GAAW9G,GAAO,kBACjB0M,UAAU,GAAKA,UAAU,GAAG,GACrBD,EAAGE,MAAM7E,KAAM4E,eAGxB5F,GAAWyF,eAAiB,SAASnM,EAAOG,UAGpCuG,GAAWpG,IAAK7B,KAAKiJ,KAAM1H,EAAOG,WAE1CuG,GAAWpG,IAAM,SAASN,EAAOG,EAAMlC,UAE/BwI,EAAYnG,IAAK7B,KAAKiJ,KAAM1H,EAAM,GAAIG,EAAMlC,EAAO+B,EAAM,SCpMpDwM,GAAb,sBAKaC,qBAJWf,yBA8BH,SAACpK,EAAWoL,EAAc1J,MAEzB,mBAAT1B,GAAyC,mBAAXoL,EAAuB,KACzDC,EAAcD,EACpBA,EAASpL,MAEHsL,EAAO9B,SACN,SAENxJ,uBAAAA,IAAAA,EAAOqL,8BACJjP,+BAAAA,2BAEIkP,EAAKC,QAAQvL,YAAOwC,kBAAmB4I,GAAOjO,cAAKyM,EAAMpH,UAAUpG,YAQxEwG,KAJkB,mBAAXwI,GAAuBlP,EAAI,YAClCwF,GAAwD,mBAAlBA,GACzCxF,EAAI,GAKDW,EAAYmD,GAAO,KAChByB,EAAQU,EAAWqH,GACnBhE,EAAQX,EAAY2E,EAAMxJ,UAC5BwL,SAEH5I,EAASwI,EAAO5F,GAChBgG,aAGIA,EAAU1J,EAAYL,GACrBM,EAAWN,SAEM,oBAAZgK,SAA2B7I,aAAkB6I,QAChD7I,EAAO8I,eACb9I,UACCpB,EAAkBC,EAAOC,GAClBiB,EAAcC,EAAQnB,eAE9BtF,SACC2F,EAAYL,GACNtF,MAITqF,EAAkBC,EAAOC,GAClBiB,EAAcC,EAAQnB,IACvB,IAAKzB,GAAwB,iBAATA,EAAmB,cAC7C4C,EAASwI,EAAOpL,MACU4C,EAAS5C,GAC/B4C,IAAWS,IAAST,UACpB4G,EAAKlF,GAAa5D,EAAOkC,MACzBlB,EAAe,KACZmG,EAAa,GACb8D,EAAc,GACpB3K,EAAU,WAAWoC,EAA4BpD,EAAM4C,EAAQiF,EAAG8D,GAClEjK,EAAcmG,EAAG8D,UAEX/I,EACD1G,EAAI,GAAI8D,4BAG0B,SAACA,EAAWoL,MAEjC,mBAATpL,SACH,SAACtB,8BAAetC,+BAAAA,2BACtBoN,EAAKoC,mBAAmBlN,YAAQ8D,UAAexC,gBAAKwC,UAAUpG,YAG5DqL,EAAkBQ,EAChBrF,EAAS4G,EAAK+B,QAAQvL,EAAMoL,YAASvD,EAAY8D,GACtDlE,EAAUI,EACVI,EAAiB0D,WAGK,oBAAZF,SAA2B7I,aAAkB6I,QAChD7I,EAAO8I,eAAKG,SAAa,CAACA,EAAWpE,EAAUQ,MAEhD,CAACrF,EAAQ6E,EAAUQ,IAzGQ,kBAAvBkD,MAAAA,SAAAA,EAAQW,aAClB1F,KAAK2F,cAAcZ,EAAQW,YACM,kBAAvBX,MAAAA,SAAAA,EAAQa,aAClB5F,KAAK6F,cAAcd,EAAQa,uCAyG7BE,YAAA,SAAiClM,GAC3BnD,EAAYmD,IAAO9D,EAAI,GACxBQ,EAAQsD,KAAOA,EAAO2F,EAAQ3F,QAC5ByB,EAAQU,EAAWiE,MACnBZ,EAAQX,EAAYuB,KAAMpG,iBAChCwF,EAAM5I,GAAaqI,KACnBlD,EAAWN,GACJ+D,KAGR2G,YAAA,SACC3J,EACAd,OAOeD,GALWe,GAAUA,EAAc5F,IAK3C8G,SACPlC,EAAkBC,EAAOC,GAClBiB,SAAyBlB,MAQjCwK,cAAA,SAActP,QACR2H,EAAc3H,KASpBoP,cAAA,SAAcpP,GACTA,IAAUyN,GACblO,EAAI,SAEA6G,EAAcpG,KAGpByP,aAAA,SAAkCpM,EAASyH,OAGtCrH,MACCA,EAAIqH,EAAQnL,OAAS,EAAG8D,GAAK,EAAGA,IAAK,KACnCsH,EAAQD,EAAQrH,MACI,IAAtBsH,EAAMnE,KAAKjH,QAA6B,YAAboL,EAAMC,GAAkB,CACtD3H,EAAO0H,EAAM/K,aAMXyD,GAAK,IACRqH,EAAUA,EAAQxH,MAAMG,EAAI,QAGvBiM,EAAmBrL,EAAU,WAAWwG,SAC1C9K,EAAQsD,GAEJqM,EAAiBrM,EAAMyH,GAGxBrB,KAAKmF,QAAQvL,YAAOwC,UAC1B6J,EAAiB7J,EAAOiF,SAxL3B,GMZMrF,GAAQ,IAAI8I,GAqBLK,GAAoBnJ,GAAMmJ,QAO1BK,GAA0CxJ,GAAMwJ,mBAAmBU,KAC/ElK,IAQY6J,GAAgB7J,GAAM6J,cAAcK,KAAKlK,IAQzC2J,GAAgB3J,GAAM2J,cAAcO,KAAKlK,IAOzCgK,GAAehK,GAAMgK,aAAaE,KAAKlK,IAMvC8J,GAAc9J,GAAM8J,YAAYI,KAAKlK,IAUrC+J,GAAc/J,GAAM+J,YAAYG,KAAKlK,sDAQrBzF,UACrBA,4BAQyBA,UACzBA,2ECvGPuJ,IACAoC,IACArB,4JZkDwBtK,UACnBD,EAAQC,IAAQT,EAAI,GAAIS,GACtBA,EAAMC,GAAakD"}