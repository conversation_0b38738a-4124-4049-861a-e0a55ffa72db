{"name": "normalize-url", "version": "1.9.1", "description": "Normalize a URL", "license": "MIT", "repository": "sindresorhus/normalize-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["normalize", "url", "uri", "address", "string", "str", "normalise", "normalization", "normalisation", "query", "string", "querystring", "unicode", "simplify", "strip", "trim", "canonical"], "dependencies": {"object-assign": "^4.0.1", "prepend-http": "^1.0.0", "query-string": "^4.1.0", "sort-keys": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "^0.16.0"}}