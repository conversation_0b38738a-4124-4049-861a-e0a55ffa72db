# TODO: Enable os: windows-latest
# TODO: Enable pytest --doctest-modules

name: Python_tests
on: [push, pull_request]
jobs:
  Python_tests:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      max-parallel: 8
      matrix:
        os: [macos-latest, ubuntu-latest] # , windows-latest]
        python-version: [3.6, 3.7, 3.8, 3.9]
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements_dev.txt
      - name: Lint with flake8
        run: flake8 . --ignore=E203,W503  --max-complexity=101 --max-line-length=88 --show-source --statistics
      - name: Test with pytest
        run: pytest
      # - name: Run doctests with pytest
      #   run: pytest --doctest-modules
