<!doctype html>
<html>
<head>
	<meta charset='utf-8' />
	<title>postcss 7.0.18 | Documentation</title>
	<meta name='description' content='Tool for transforming styles with JS plugins'>
	<meta name='viewport' content='width=device-width,initial-scale=1'>
	<link href='assets/styles.min.css' rel='stylesheet' />
</head>

<body class='documentation'>
<div class='px2'>
	<div class='clearfix md-flex lg-flex flex-stretch mxn2'>
		<div class='documentation-sidebar relative top-0 bottom-0 right-0 px2 py2 col-3 md-show'>
			<div class='font-smaller fixed col-3 top-0 bottom-0 left-0 overflow-auto fill-light dark-link'>
				<div class='px2'>
					<h3 class='mb0 no-anchor'><code>postcss</code></h3>
					<div class='mb1'><code>7.0.18</code></div>
					<input placeholder='Filter' id='filter-input' class='col12 block input' type='text' />
					<div id="toc">
						
						
						
						<a
								href='#classes'
								class="blockmt1 quiet rounded bold block h4 mt2 ">
							<code>CLASSES</code>
							
						</a>
						
						
						
						
						
						<a
								href='#atrule'
								class="regular block toggle-sibling">
							<code>AtRule</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#atruleappend'
									class='button-indent regular block'>
								<code>#append</code>
							</a>
							
							<a
									href='#atruleeach'
									class='button-indent regular block'>
								<code>#each</code>
							</a>
							
							<a
									href='#atruleevery'
									class='button-indent regular block'>
								<code>#every</code>
							</a>
							
							<a
									href='#atrulefirst'
									class='button-indent regular block'>
								<code>#first</code>
							</a>
							
							<a
									href='#atruleindex'
									class='button-indent regular block'>
								<code>#index</code>
							</a>
							
							<a
									href='#atruleinsertafter'
									class='button-indent regular block'>
								<code>#insertAfter</code>
							</a>
							
							<a
									href='#atruleinsertbefore'
									class='button-indent regular block'>
								<code>#insertBefore</code>
							</a>
							
							<a
									href='#atrulelast'
									class='button-indent regular block'>
								<code>#last</code>
							</a>
							
							<a
									href='#atruleprepend'
									class='button-indent regular block'>
								<code>#prepend</code>
							</a>
							
							<a
									href='#atruleremoveall'
									class='button-indent regular block'>
								<code>#removeAll</code>
							</a>
							
							<a
									href='#atruleremovechild'
									class='button-indent regular block'>
								<code>#removeChild</code>
							</a>
							
							<a
									href='#atrulereplacevalues'
									class='button-indent regular block'>
								<code>#replaceValues</code>
							</a>
							
							<a
									href='#atrulesome'
									class='button-indent regular block'>
								<code>#some</code>
							</a>
							
							<a
									href='#atrulewalk'
									class='button-indent regular block'>
								<code>#walk</code>
							</a>
							
							<a
									href='#atrulewalkatrules'
									class='button-indent regular block'>
								<code>#walkAtRules</code>
							</a>
							
							<a
									href='#atrulewalkcomments'
									class='button-indent regular block'>
								<code>#walkComments</code>
							</a>
							
							<a
									href='#atrulewalkdecls'
									class='button-indent regular block'>
								<code>#walkDecls</code>
							</a>
							
							<a
									href='#atrulewalkrules'
									class='button-indent regular block'>
								<code>#walkRules</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#comment'
								class="regular block toggle-sibling">
							<code>Comment</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#commentafter'
									class='button-indent regular block'>
								<code>#after</code>
							</a>
							
							<a
									href='#commentbefore'
									class='button-indent regular block'>
								<code>#before</code>
							</a>
							
							<a
									href='#commentcleanraws'
									class='button-indent regular block'>
								<code>#cleanRaws</code>
							</a>
							
							<a
									href='#commentclone'
									class='button-indent regular block'>
								<code>#clone</code>
							</a>
							
							<a
									href='#commentcloneafter'
									class='button-indent regular block'>
								<code>#cloneAfter</code>
							</a>
							
							<a
									href='#commentclonebefore'
									class='button-indent regular block'>
								<code>#cloneBefore</code>
							</a>
							
							<a
									href='#commenterror'
									class='button-indent regular block'>
								<code>#error</code>
							</a>
							
							<a
									href='#commentnext'
									class='button-indent regular block'>
								<code>#next</code>
							</a>
							
							<a
									href='#commentprev'
									class='button-indent regular block'>
								<code>#prev</code>
							</a>
							
							<a
									href='#commentraw'
									class='button-indent regular block'>
								<code>#raw</code>
							</a>
							
							<a
									href='#commentremove'
									class='button-indent regular block'>
								<code>#remove</code>
							</a>
							
							<a
									href='#commentreplacewith'
									class='button-indent regular block'>
								<code>#replaceWith</code>
							</a>
							
							<a
									href='#commentroot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#commenttostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#commentwarn'
									class='button-indent regular block'>
								<code>#warn</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#container'
								class="regular block toggle-sibling">
							<code>Container</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#containerafter'
									class='button-indent regular block'>
								<code>#after</code>
							</a>
							
							<a
									href='#containerappend'
									class='button-indent regular block'>
								<code>#append</code>
							</a>
							
							<a
									href='#containerbefore'
									class='button-indent regular block'>
								<code>#before</code>
							</a>
							
							<a
									href='#containercleanraws'
									class='button-indent regular block'>
								<code>#cleanRaws</code>
							</a>
							
							<a
									href='#containerclone'
									class='button-indent regular block'>
								<code>#clone</code>
							</a>
							
							<a
									href='#containercloneafter'
									class='button-indent regular block'>
								<code>#cloneAfter</code>
							</a>
							
							<a
									href='#containerclonebefore'
									class='button-indent regular block'>
								<code>#cloneBefore</code>
							</a>
							
							<a
									href='#containereach'
									class='button-indent regular block'>
								<code>#each</code>
							</a>
							
							<a
									href='#containererror'
									class='button-indent regular block'>
								<code>#error</code>
							</a>
							
							<a
									href='#containerevery'
									class='button-indent regular block'>
								<code>#every</code>
							</a>
							
							<a
									href='#containerfirst'
									class='button-indent regular block'>
								<code>#first</code>
							</a>
							
							<a
									href='#containerindex'
									class='button-indent regular block'>
								<code>#index</code>
							</a>
							
							<a
									href='#containerinsertafter'
									class='button-indent regular block'>
								<code>#insertAfter</code>
							</a>
							
							<a
									href='#containerinsertbefore'
									class='button-indent regular block'>
								<code>#insertBefore</code>
							</a>
							
							<a
									href='#containerlast'
									class='button-indent regular block'>
								<code>#last</code>
							</a>
							
							<a
									href='#containernext'
									class='button-indent regular block'>
								<code>#next</code>
							</a>
							
							<a
									href='#containerprepend'
									class='button-indent regular block'>
								<code>#prepend</code>
							</a>
							
							<a
									href='#containerprev'
									class='button-indent regular block'>
								<code>#prev</code>
							</a>
							
							<a
									href='#containerraw'
									class='button-indent regular block'>
								<code>#raw</code>
							</a>
							
							<a
									href='#containerremove'
									class='button-indent regular block'>
								<code>#remove</code>
							</a>
							
							<a
									href='#containerremoveall'
									class='button-indent regular block'>
								<code>#removeAll</code>
							</a>
							
							<a
									href='#containerremovechild'
									class='button-indent regular block'>
								<code>#removeChild</code>
							</a>
							
							<a
									href='#containerreplacevalues'
									class='button-indent regular block'>
								<code>#replaceValues</code>
							</a>
							
							<a
									href='#containerreplacewith'
									class='button-indent regular block'>
								<code>#replaceWith</code>
							</a>
							
							<a
									href='#containerroot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#containersome'
									class='button-indent regular block'>
								<code>#some</code>
							</a>
							
							<a
									href='#containertostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#containerwalk'
									class='button-indent regular block'>
								<code>#walk</code>
							</a>
							
							<a
									href='#containerwalkatrules'
									class='button-indent regular block'>
								<code>#walkAtRules</code>
							</a>
							
							<a
									href='#containerwalkcomments'
									class='button-indent regular block'>
								<code>#walkComments</code>
							</a>
							
							<a
									href='#containerwalkdecls'
									class='button-indent regular block'>
								<code>#walkDecls</code>
							</a>
							
							<a
									href='#containerwalkrules'
									class='button-indent regular block'>
								<code>#walkRules</code>
							</a>
							
							<a
									href='#containerwarn'
									class='button-indent regular block'>
								<code>#warn</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#csssyntaxerror'
								class="regular block toggle-sibling">
							<code>CssSyntaxError</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#csssyntaxerrorname'
									class='button-indent regular block'>
								<code>#name</code>
							</a>
							
							<a
									href='#csssyntaxerrorreason'
									class='button-indent regular block'>
								<code>#reason</code>
							</a>
							
							<a
									href='#csssyntaxerrorfile'
									class='button-indent regular block'>
								<code>#file</code>
							</a>
							
							<a
									href='#csssyntaxerrorsource'
									class='button-indent regular block'>
								<code>#source</code>
							</a>
							
							<a
									href='#csssyntaxerrorplugin'
									class='button-indent regular block'>
								<code>#plugin</code>
							</a>
							
							<a
									href='#csssyntaxerrorline'
									class='button-indent regular block'>
								<code>#line</code>
							</a>
							
							<a
									href='#csssyntaxerrorcolumn'
									class='button-indent regular block'>
								<code>#column</code>
							</a>
							
							<a
									href='#csssyntaxerrormessage'
									class='button-indent regular block'>
								<code>#message</code>
							</a>
							
							<a
									href='#csssyntaxerrorshowsourcecode'
									class='button-indent regular block'>
								<code>#showSourceCode</code>
							</a>
							
							<a
									href='#csssyntaxerrortostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#declaration'
								class="regular block toggle-sibling">
							<code>Declaration</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#declarationafter'
									class='button-indent regular block'>
								<code>#after</code>
							</a>
							
							<a
									href='#declarationbefore'
									class='button-indent regular block'>
								<code>#before</code>
							</a>
							
							<a
									href='#declarationcleanraws'
									class='button-indent regular block'>
								<code>#cleanRaws</code>
							</a>
							
							<a
									href='#declarationclone'
									class='button-indent regular block'>
								<code>#clone</code>
							</a>
							
							<a
									href='#declarationcloneafter'
									class='button-indent regular block'>
								<code>#cloneAfter</code>
							</a>
							
							<a
									href='#declarationclonebefore'
									class='button-indent regular block'>
								<code>#cloneBefore</code>
							</a>
							
							<a
									href='#declarationerror'
									class='button-indent regular block'>
								<code>#error</code>
							</a>
							
							<a
									href='#declarationnext'
									class='button-indent regular block'>
								<code>#next</code>
							</a>
							
							<a
									href='#declarationprev'
									class='button-indent regular block'>
								<code>#prev</code>
							</a>
							
							<a
									href='#declarationraw'
									class='button-indent regular block'>
								<code>#raw</code>
							</a>
							
							<a
									href='#declarationremove'
									class='button-indent regular block'>
								<code>#remove</code>
							</a>
							
							<a
									href='#declarationreplacewith'
									class='button-indent regular block'>
								<code>#replaceWith</code>
							</a>
							
							<a
									href='#declarationroot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#declarationtostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#declarationwarn'
									class='button-indent regular block'>
								<code>#warn</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#input'
								class="regular block toggle-sibling">
							<code>Input</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#inputcss'
									class='button-indent regular block'>
								<code>#css</code>
							</a>
							
							<a
									href='#inputfile'
									class='button-indent regular block'>
								<code>#file</code>
							</a>
							
							<a
									href='#inputmap'
									class='button-indent regular block'>
								<code>#map</code>
							</a>
							
							<a
									href='#inputid'
									class='button-indent regular block'>
								<code>#id</code>
							</a>
							
							<a
									href='#inputorigin'
									class='button-indent regular block'>
								<code>#origin</code>
							</a>
							
							<a
									href='#inputfrom'
									class='button-indent regular block'>
								<code>#from</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#lazyresult'
								class="regular block toggle-sibling">
							<code>LazyResult</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#lazyresultprocessor'
									class='button-indent regular block'>
								<code>#processor</code>
							</a>
							
							<a
									href='#lazyresultopts'
									class='button-indent regular block'>
								<code>#opts</code>
							</a>
							
							<a
									href='#lazyresultcss'
									class='button-indent regular block'>
								<code>#css</code>
							</a>
							
							<a
									href='#lazyresultcontent'
									class='button-indent regular block'>
								<code>#content</code>
							</a>
							
							<a
									href='#lazyresultmap'
									class='button-indent regular block'>
								<code>#map</code>
							</a>
							
							<a
									href='#lazyresultroot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#lazyresultmessages'
									class='button-indent regular block'>
								<code>#messages</code>
							</a>
							
							<a
									href='#lazyresultwarnings'
									class='button-indent regular block'>
								<code>#warnings</code>
							</a>
							
							<a
									href='#lazyresulttostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#lazyresultthen'
									class='button-indent regular block'>
								<code>#then</code>
							</a>
							
							<a
									href='#lazyresultcatch'
									class='button-indent regular block'>
								<code>#catch</code>
							</a>
							
							<a
									href='#lazyresultfinally'
									class='button-indent regular block'>
								<code>#finally</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#node'
								class="regular block toggle-sibling">
							<code>Node</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#nodeerror'
									class='button-indent regular block'>
								<code>#error</code>
							</a>
							
							<a
									href='#nodewarn'
									class='button-indent regular block'>
								<code>#warn</code>
							</a>
							
							<a
									href='#noderemove'
									class='button-indent regular block'>
								<code>#remove</code>
							</a>
							
							<a
									href='#nodetostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#nodeclone'
									class='button-indent regular block'>
								<code>#clone</code>
							</a>
							
							<a
									href='#nodeclonebefore'
									class='button-indent regular block'>
								<code>#cloneBefore</code>
							</a>
							
							<a
									href='#nodecloneafter'
									class='button-indent regular block'>
								<code>#cloneAfter</code>
							</a>
							
							<a
									href='#nodereplacewith'
									class='button-indent regular block'>
								<code>#replaceWith</code>
							</a>
							
							<a
									href='#nodenext'
									class='button-indent regular block'>
								<code>#next</code>
							</a>
							
							<a
									href='#nodeprev'
									class='button-indent regular block'>
								<code>#prev</code>
							</a>
							
							<a
									href='#nodebefore'
									class='button-indent regular block'>
								<code>#before</code>
							</a>
							
							<a
									href='#nodeafter'
									class='button-indent regular block'>
								<code>#after</code>
							</a>
							
							<a
									href='#noderaw'
									class='button-indent regular block'>
								<code>#raw</code>
							</a>
							
							<a
									href='#noderoot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#nodecleanraws'
									class='button-indent regular block'>
								<code>#cleanRaws</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#previousmap'
								class="regular block toggle-sibling">
							<code>PreviousMap</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#previousmapinline'
									class='button-indent regular block'>
								<code>#inline</code>
							</a>
							
							<a
									href='#previousmapconsumer'
									class='button-indent regular block'>
								<code>#consumer</code>
							</a>
							
							<a
									href='#previousmapwithcontent'
									class='button-indent regular block'>
								<code>#withContent</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#processor'
								class="regular block toggle-sibling">
							<code>Processor</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#processorversion'
									class='button-indent regular block'>
								<code>#version</code>
							</a>
							
							<a
									href='#processorplugins'
									class='button-indent regular block'>
								<code>#plugins</code>
							</a>
							
							<a
									href='#processoruse'
									class='button-indent regular block'>
								<code>#use</code>
							</a>
							
							<a
									href='#processorprocess'
									class='button-indent regular block'>
								<code>#process</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#result'
								class="regular block toggle-sibling">
							<code>Result</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#resultprocessor'
									class='button-indent regular block'>
								<code>#processor</code>
							</a>
							
							<a
									href='#resultmessages'
									class='button-indent regular block'>
								<code>#messages</code>
							</a>
							
							<a
									href='#resultroot'
									class='button-indent regular block'>
								<code>#root</code>
							</a>
							
							<a
									href='#resultopts'
									class='button-indent regular block'>
								<code>#opts</code>
							</a>
							
							<a
									href='#resultcss'
									class='button-indent regular block'>
								<code>#css</code>
							</a>
							
							<a
									href='#resultmap'
									class='button-indent regular block'>
								<code>#map</code>
							</a>
							
							<a
									href='#resulttostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							<a
									href='#resultwarn'
									class='button-indent regular block'>
								<code>#warn</code>
							</a>
							
							<a
									href='#resultwarnings'
									class='button-indent regular block'>
								<code>#warnings</code>
							</a>
							
							<a
									href='#resultcontent'
									class='button-indent regular block'>
								<code>#content</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#root'
								class="regular block toggle-sibling">
							<code>Root</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#rootappend'
									class='button-indent regular block'>
								<code>#append</code>
							</a>
							
							<a
									href='#rooteach'
									class='button-indent regular block'>
								<code>#each</code>
							</a>
							
							<a
									href='#rootevery'
									class='button-indent regular block'>
								<code>#every</code>
							</a>
							
							<a
									href='#rootfirst'
									class='button-indent regular block'>
								<code>#first</code>
							</a>
							
							<a
									href='#rootindex'
									class='button-indent regular block'>
								<code>#index</code>
							</a>
							
							<a
									href='#rootinsertafter'
									class='button-indent regular block'>
								<code>#insertAfter</code>
							</a>
							
							<a
									href='#rootinsertbefore'
									class='button-indent regular block'>
								<code>#insertBefore</code>
							</a>
							
							<a
									href='#rootlast'
									class='button-indent regular block'>
								<code>#last</code>
							</a>
							
							<a
									href='#rooton'
									class='button-indent regular block'>
								<code>#on</code>
							</a>
							
							<a
									href='#rootprepend'
									class='button-indent regular block'>
								<code>#prepend</code>
							</a>
							
							<a
									href='#rootremoveall'
									class='button-indent regular block'>
								<code>#removeAll</code>
							</a>
							
							<a
									href='#rootremovechild'
									class='button-indent regular block'>
								<code>#removeChild</code>
							</a>
							
							<a
									href='#rootreplacevalues'
									class='button-indent regular block'>
								<code>#replaceValues</code>
							</a>
							
							<a
									href='#rootsome'
									class='button-indent regular block'>
								<code>#some</code>
							</a>
							
							<a
									href='#roottoresult'
									class='button-indent regular block'>
								<code>#toResult</code>
							</a>
							
							<a
									href='#rootwalk'
									class='button-indent regular block'>
								<code>#walk</code>
							</a>
							
							<a
									href='#rootwalkatrules'
									class='button-indent regular block'>
								<code>#walkAtRules</code>
							</a>
							
							<a
									href='#rootwalkcomments'
									class='button-indent regular block'>
								<code>#walkComments</code>
							</a>
							
							<a
									href='#rootwalkdecls'
									class='button-indent regular block'>
								<code>#walkDecls</code>
							</a>
							
							<a
									href='#rootwalkrules'
									class='button-indent regular block'>
								<code>#walkRules</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#rule'
								class="regular block toggle-sibling">
							<code>Rule</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#ruleappend'
									class='button-indent regular block'>
								<code>#append</code>
							</a>
							
							<a
									href='#ruleeach'
									class='button-indent regular block'>
								<code>#each</code>
							</a>
							
							<a
									href='#ruleevery'
									class='button-indent regular block'>
								<code>#every</code>
							</a>
							
							<a
									href='#rulefirst'
									class='button-indent regular block'>
								<code>#first</code>
							</a>
							
							<a
									href='#ruleindex'
									class='button-indent regular block'>
								<code>#index</code>
							</a>
							
							<a
									href='#ruleinsertafter'
									class='button-indent regular block'>
								<code>#insertAfter</code>
							</a>
							
							<a
									href='#ruleinsertbefore'
									class='button-indent regular block'>
								<code>#insertBefore</code>
							</a>
							
							<a
									href='#rulelast'
									class='button-indent regular block'>
								<code>#last</code>
							</a>
							
							<a
									href='#ruleprepend'
									class='button-indent regular block'>
								<code>#prepend</code>
							</a>
							
							<a
									href='#ruleremoveall'
									class='button-indent regular block'>
								<code>#removeAll</code>
							</a>
							
							<a
									href='#ruleremovechild'
									class='button-indent regular block'>
								<code>#removeChild</code>
							</a>
							
							<a
									href='#rulereplacevalues'
									class='button-indent regular block'>
								<code>#replaceValues</code>
							</a>
							
							<a
									href='#ruleselectors'
									class='button-indent regular block'>
								<code>#selectors</code>
							</a>
							
							<a
									href='#rulesome'
									class='button-indent regular block'>
								<code>#some</code>
							</a>
							
							<a
									href='#rulewalk'
									class='button-indent regular block'>
								<code>#walk</code>
							</a>
							
							<a
									href='#rulewalkatrules'
									class='button-indent regular block'>
								<code>#walkAtRules</code>
							</a>
							
							<a
									href='#rulewalkcomments'
									class='button-indent regular block'>
								<code>#walkComments</code>
							</a>
							
							<a
									href='#rulewalkdecls'
									class='button-indent regular block'>
								<code>#walkDecls</code>
							</a>
							
							<a
									href='#rulewalkrules'
									class='button-indent regular block'>
								<code>#walkRules</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#warning'
								class="regular block toggle-sibling">
							<code>Warning</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							
							<a
									href='#warningtype'
									class='button-indent regular block'>
								<code>#type</code>
							</a>
							
							<a
									href='#warningtext'
									class='button-indent regular block'>
								<code>#text</code>
							</a>
							
							<a
									href='#warningline'
									class='button-indent regular block'>
								<code>#line</code>
							</a>
							
							<a
									href='#warningcolumn'
									class='button-indent regular block'>
								<code>#column</code>
							</a>
							
							<a
									href='#warningtostring'
									class='button-indent regular block'>
								<code>#toString</code>
							</a>
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#namespaces'
								class="blockmt1 quiet rounded bold block h4 mt2 ">
							<code>NAMESPACES</code>
							
						</a>
						
						
						
						
						
						<a
								href='#list'
								class="regular block toggle-sibling">
							<code>list</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							<a
									href='#listspace'
									class='button-indent px1 quiet regular rounded block'>
								<code>.space</code>
							</a>
							
							<a
									href='#listcomma'
									class='button-indent px1 quiet regular rounded block'>
								<code>.comma</code>
							</a>
							
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#postcss'
								class="regular block toggle-sibling">
							<code>postcss</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							<a
									href='#postcssplugin'
									class='button-indent px1 quiet regular rounded block'>
								<code>.plugin</code>
							</a>
							
							<a
									href='#postcssstringify'
									class='button-indent px1 quiet regular rounded block'>
								<code>.stringify</code>
							</a>
							
							<a
									href='#postcssparse'
									class='button-indent px1 quiet regular rounded block'>
								<code>.parse</code>
							</a>
							
							<a
									href='#postcssvendor'
									class='button-indent px1 quiet regular rounded block'>
								<code>.vendor</code>
							</a>
							
							<a
									href='#postcsslist'
									class='button-indent px1 quiet regular rounded block'>
								<code>.list</code>
							</a>
							
							<a
									href='#postcsscomment'
									class='button-indent px1 quiet regular rounded block'>
								<code>.comment</code>
							</a>
							
							<a
									href='#postcssatrule'
									class='button-indent px1 quiet regular rounded block'>
								<code>.atRule</code>
							</a>
							
							<a
									href='#postcssdecl'
									class='button-indent px1 quiet regular rounded block'>
								<code>.decl</code>
							</a>
							
							<a
									href='#postcssrule'
									class='button-indent px1 quiet regular rounded block'>
								<code>.rule</code>
							</a>
							
							<a
									href='#postcssroot'
									class='button-indent px1 quiet regular rounded block'>
								<code>.root</code>
							</a>
							
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#vendor'
								class="regular block toggle-sibling">
							<code>vendor</code>
							<span class='icon'>▾</span>
						</a>
						
						
						<div class='toggle-target'>
							
							
							<a
									href='#vendorprefix'
									class='button-indent px1 quiet regular rounded block'>
								<code>.prefix</code>
							</a>
							
							<a
									href='#vendorunprefixed'
									class='button-indent px1 quiet regular rounded block'>
								<code>.unprefixed</code>
							</a>
							
							
							
							
							
						</div>
						
						
						
						
						<a
								href='#global'
								class="blockmt1 quiet rounded bold block h4 mt2 ">
							<code>GLOBAL</code>
							
						</a>
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
						
					</div>
					<div class='mt1 h6 quiet'>
						<a href='https://documentation.js.org/reading-documentation.html'>Need help reading this?</a>
					</div>
				</div>

			</div>
		</div>
		<div class='sm-col-12 md-col-9 lg-col-9 flex flex-column'>
			<div class='flex-auto full-width'>
				
				
				<div class="hide">
  <section class='py2 clearfix'>

    <h2 id='classes' class='mt0'>
      CLASSES
    </h2>

    
      

    
  </section>
</div>
				
				
				
				<section id='atrule'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#container">Container</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/at-rule.js#L21-L90'>
			<span>lib/at-rule.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrule'>
			<code>
				AtRule
				<span class='gray'>(defaults)</span>
			</code>
		</a>
	</h3>

	<p>Represents an at-rule.</p>
<p>If it’s followed in the CSS by a {} block, this node will have
a nodes property representing its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'@charset "UTF-8"; @media print {}'</span>)

<span class="hljs-keyword">const</span> charset = root.first
charset.type  <span class="hljs-comment">//=&gt; 'atrule'</span>
charset.nodes <span class="hljs-comment">//=&gt; undefined</span>

<span class="hljs-keyword">const</span> media = root.last
media.nodes   <span class="hljs-comment">//=&gt; []</span></code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='atruleappend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L322-L331'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleappend'>
			<code>
				append
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the end of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.append(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleeach'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L63-L89'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleeach'>
			<code>
				each
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Iterates through the container’s immediate children,
calling <code>callback</code> for each child.</p>
<p>Returning <code>false</code> in the callback will break iteration.</p>
<p>This method only iterates through the container’s immediate children.
If you need to recursively iterate through all the container’s descendant
nodes, use <a href="#containerwalk">Container#walk</a>.</p>
<p>Unlike the for <code>{}</code>-cycle or <code>Array#forEach</code> this iterator is safe
if you are mutating the array of child nodes during iteration.
PostCSS will adjust the current index to match the mutations.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { color: black; z-index: 1 }'</span>)
<span class="hljs-keyword">const</span> rule = root.first

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> decl <span class="hljs-keyword">of</span> rule.nodes) {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Cycle will be infinite, because cloneBefore moves the current node</span>
  <span class="hljs-comment">// to the next index</span>
}

rule.each(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Will be executed only for color and z-index</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleevery'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L539-L541'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleevery'>
			<code>
				every
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code>
for all of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is every child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> noPrefixes = rule.every(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] !== <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulefirst'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L582-L585'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulefirst'>
			<code>
				first
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s first child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.first === rules.nodes[<span class="hljs-number">0</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleindex'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L568-L572'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleindex'>
			<code>
				index
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>child</code>’s index within the <a href="Container#nodes">Container#nodes</a> array.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Child of the current container.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
	:
	<span class='force-inline'>Child index.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.index( rule.nodes[<span class="hljs-number">2</span>] ) <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleinsertafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L414-L431'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleinsertafter'>
			<code>
				insertAfter
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='atruleinsertbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L386-L404'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleinsertbefore'>
			<code>
				insertBefore
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.insertBefore(decl, decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop }))</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulelast'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L595-L598'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulelast'>
			<code>
				last
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s last child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.last === rule.nodes[rule.nodes.length - <span class="hljs-number">1</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleprepend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L353-L366'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleprepend'>
			<code>
				prepend
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the start of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.prepend(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleremoveall'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L475-L482'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleremoveall'>
			<code>
				removeAll
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes all children from the container
and cleans their parent properties.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.removeAll()
rule.nodes.length <span class="hljs-comment">//=&gt; 0</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atruleremovechild'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L447-L463'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atruleremovechild'>
			<code>
				removeChild
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Removes node from the container and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.nodes.length  <span class="hljs-comment">//=&gt; 5</span>
rule.removeChild(decl)
rule.nodes.length  <span class="hljs-comment">//=&gt; 4</span>
decl.parent        <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulereplacevalues'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L510-L526'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulereplacevalues'>
			<code>
				replaceValues
				<span class='gray'>(pattern, opts, callback)</span>
			</code>
		</a>
	</h3>

	<p>Passes all declaration values within the container that match pattern
through callback, replacing those values with the returned result
of callback.</p>
<p>This method is useful if you are using a custom unit or function
and need to iterate through all values.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>pattern</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)
				
			</td>
			<td class='col-6'>Replace pattern.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-6'>Options to speed up the search.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.props</td>
  <td class="col-2 quiet">
    (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)
    
  </td>
  <td class='col-8'>An array of property names.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.fast</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>String that’s used to narrow down
values and speed up the regexp search.
</td>
</tr>


		
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
				
			</td>
			<td class='col-6'>String to replace pattern or callback
that returns a new value. The callback
will receive the same arguments
as those passed to a function parameter
of 
<code>String#replace</code>
.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.replaceValues(<span class="hljs-regexp">/\d+rem/</span>, { <span class="hljs-attr">fast</span>: <span class="hljs-string">'rem'</span> }, string =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-number">15</span> * <span class="hljs-built_in">parseInt</span>(string) + <span class="hljs-string">'px'</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulesome'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L554-L556'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulesome'>
			<code>
				some
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code> for (at least) one
of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is some child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> hasPrefix = rule.some(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] === <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulewalk'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L110-L124'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulewalk'>
			<code>
				walk
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each node.</p>
<p>Like container.each(), this method is safe to use
if you are mutating arrays during iteration.</p>
<p>If you only need to iterate through the container’s immediate children,
use <a href="#containereach">Container#each</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walk(<span class="hljs-function"><span class="hljs-params">node</span> =&gt;</span> {
  <span class="hljs-comment">// Traverses all descendant nodes.</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulewalkatrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L255-L276'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulewalkatrules'>
			<code>
				walkAtRules
				<span class='gray'>(name?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each at-rule node.</p>
<p>If you pass a filter, iteration will only happen over at-rules
that have matching names.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>name</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter at-rules by name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkAtRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  <span class="hljs-keyword">if</span> (isOld(rule.name)) rule.remove()
})

<span class="hljs-keyword">let</span> first = <span class="hljs-literal">false</span>
root.walkAtRules(<span class="hljs-string">'charset'</span>, rule =&gt; {
  <span class="hljs-keyword">if</span> (!first) {
    first = <span class="hljs-literal">true</span>
  } <span class="hljs-keyword">else</span> {
    rule.remove()
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulewalkcomments'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L294-L300'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulewalkcomments'>
			<code>
				walkComments
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each comment node.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkComments(<span class="hljs-function"><span class="hljs-params">comment</span> =&gt;</span> {
  comment.remove()
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulewalkdecls'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L155-L176'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulewalkdecls'>
			<code>
				walkDecls
				<span class='gray'>(prop?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each declaration node.</p>
<p>If you pass a filter, iteration will only happen over declarations
with matching properties.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter declarations by property name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkDecls(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  checkPropertySupport(decl.prop)
})

root.walkDecls(<span class="hljs-string">'border-radius'</span>, decl =&gt; {
  decl.remove()
})

root.walkDecls(<span class="hljs-regexp">/^background/</span>, decl =&gt; {
  decl.value = takeFirstColorFromGradient(decl.value)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='atrulewalkrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L201-L223'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#atrulewalkrules'>
			<code>
				walkRules
				<span class='gray'>(selector?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each rule node.</p>
<p>If you pass a filter, iteration will only happen over rules
with matching selectors.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>selector</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter rules by selector.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> selectors = []
root.walkRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  selectors.push(rule.selector)
})
<span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Your CSS uses <span class="hljs-subst">${ selectors.length }</span> selectors`</span>)</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='comment'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#node">Node</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/comment.js#L11-L34'>
			<span>lib/comment.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#comment'>
			<code>
				Comment
				<span class='gray'>(defaults)</span>
			</code>
		</a>
	</h3>

	<p>Represents a comment between declarations or statements (rule and at-rules).</p>
<p>Comments inside selectors, at-rule parameters, or declaration values
will be stored in the <code>raws</code> properties explained above.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='commentafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L313-L316'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentafter'>
			<code>
				after
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertAfter(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.after(<span class="hljs-string">'color: black'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L296-L299'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentbefore'>
			<code>
				before
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertBefore(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.before(<span class="hljs-string">'content: ""'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentcleanraws'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L394-L398'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentcleanraws'>
			<code>
				cleanRaws
				<span class='gray'>(keepBetween?)</span>
			</code>
		</a>
	</h3>

	<p>Clear the code style properties for the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>keepBetween</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
				
			</td>
			<td class='col-6'>Keep the raws.between symbols.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>node.raws.before  <span class="hljs-comment">//=&gt; ' '</span>
node.cleanRaws()
node.raws.before  <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentclone'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L183-L189'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentclone'>
			<code>
				clone
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns an exact clone of the node.</p>
<p>The resulting cloned node and its (cloned) children will retain
code style properties.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Clone of the node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.raws.before    <span class="hljs-comment">//=&gt; "\n  "</span>
<span class="hljs-keyword">const</span> cloned = decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })
cloned.raws.before  <span class="hljs-comment">//=&gt; "\n  "</span>
cloned.toString()   <span class="hljs-comment">//=&gt; -moz-transform: scale(0)</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentcloneafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L216-L220'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentcloneafter'>
			<code>
				cloneAfter
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
after the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='commentclonebefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L202-L206'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentclonebefore'>
			<code>
				cloneBefore
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
before the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Mew properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commenterror'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L89-L95'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commenterror'>
			<code>
				error
				<span class='gray'>(message, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>CssSyntaxError</code> instance containing the original position
of the node in the source, showing line and column numbers and also
a small excerpt to facilitate debugging.</p>
<p>If present, an input source map will be used to get the original position
of the source, even from a previous compilation step
(e.g., from Sass compilation).</p>
<p>This method produces very useful error messages.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>message</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Error description.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this error.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#csssyntaxerror">CssSyntaxError</a></code>
	:
	<span class='force-inline'>Error object to throw it.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (!variables[name]) {
  <span class="hljs-keyword">throw</span> decl.error(<span class="hljs-string">'Unknown variable '</span> + name, { <span class="hljs-attr">word</span>: name })
  <span class="hljs-comment">// CssSyntaxError: postcss-vars:a.sass:4:3: Unknown variable $black</span>
  <span class="hljs-comment">//   color: $black</span>
  <span class="hljs-comment">// a</span>
  <span class="hljs-comment">//          ^</span>
  <span class="hljs-comment">//   background: white</span>
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentnext'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L260-L264'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentnext'>
			<code>
				next
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the next child of the node’s parent.
Returns <code>undefined</code> if the current node is the last child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Next node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (comment.text === <span class="hljs-string">'delete next'</span>) {
  <span class="hljs-keyword">const</span> next = comment.next()
  <span class="hljs-keyword">if</span> (next) {
    next.remove()
  }
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentprev'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L278-L282'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentprev'>
			<code>
				prev
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the previous child of the node’s parent.
Returns <code>undefined</code> if the current node is the first child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Previous node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> annotation = decl.prev()
<span class="hljs-keyword">if</span> (annotation.type === <span class="hljs-string">'comment'</span>) {
  readAnnotation(annotation.text)
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentraw'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L363-L366'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentraw'>
			<code>
				raw
				<span class='gray'>(prop, defaultType?)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="Node#raws">Node#raws</a> value. If the node is missing
the code style property (because the node was manually built or cloned),
PostCSS will try to autodetect the code style property by looking
at other nodes in the tree.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Name of code style property.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>defaultType</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Name of default value, it can be missed
if the value is the same as prop.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Code style value.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { background: white }'</span>)
root.nodes[<span class="hljs-number">0</span>].append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raws.before   <span class="hljs-comment">//=&gt; undefined</span>
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raw(<span class="hljs-string">'before'</span>) <span class="hljs-comment">//=&gt; ' '</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentremove'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L139-L145'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentremove'>
			<code>
				remove
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes the node from its parent and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Node to make calls chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (decl.prop.match(<span class="hljs-regexp">/^-webkit-/</span>)) {
  decl.remove()
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentreplacewith'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L234-L244'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentreplacewith'>
			<code>
				replaceWith
				<span class='gray'>(nodes)</span>
			</code>
		</a>
	</h3>

	<p>Inserts node(s) before the current node and removes the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>nodes</code></td>
			<td class='col-3 quiet'>
				...<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Mode(s) to replace current one.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Current node to methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (atrule.name === <span class="hljs-string">'mixin'</span>) {
  atrule.replaceWith(mixinRules[atrule.params])
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L376-L380'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentroot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Finds the Root instance of the node’s tree.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>Root parent.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">0</span>].root() === root</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commenttostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L158-L165'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commenttostring'>
			<code>
				toString
				<span class='gray'>(stringifier = stringify)</span>
			</code>
		</a>
	</h3>

	<p>Returns a CSS string representing the node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>stringifier</code></td>
			<td class='col-3 quiet'>
				(<a href="#stringifier">stringifier</a> | <a href="#syntax">syntax</a>)?
				
				= <code>stringify</code>
			</td>
			<td class='col-6'>A syntax to use
in string generation.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>CSS string of this node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.rule({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> }).toString() <span class="hljs-comment">//=&gt; "a {}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='commentwarn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L122-L126'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#commentwarn'>
			<code>
				warn
				<span class='gray'>(result, text, opts?)</span>
			</code>
		</a>
	</h3>

	<p>This method is provided as a convenience wrapper for <a href="#resultwarn">Result#warn</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'>The 
<a href="#result">Result</a>
 instance
that will receive the warning.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Options
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this warning.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#warning">Warning</a></code>
	:
	<span class='force-inline'>Created warning object.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> plugin = postcss.plugin(<span class="hljs-string">'postcss-deprecated'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    root.walkDecls(<span class="hljs-string">'bad'</span>, decl =&gt; {
      decl.warn(result, <span class="hljs-string">'Deprecated property bad'</span>)
    })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='container'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#node">Node</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L23-L693'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#container'>
			<code>
				Container
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The <a href="#root">Root</a>, <a href="#atrule">AtRule</a>, and <a href="#rule">Rule</a> container nodes
inherit some common methods to help work with their children.</p>
<p>Note that all containers can store any content. If you write a rule inside
a rule, PostCSS will parse it.</p>

	

	
	
	
	
	
	

	

	

	

	

	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='containerafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L313-L316'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerafter'>
			<code>
				after
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertAfter(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.after(<span class="hljs-string">'color: black'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerappend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L322-L331'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerappend'>
			<code>
				append
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the end of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.append(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L296-L299'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerbefore'>
			<code>
				before
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertBefore(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.before(<span class="hljs-string">'content: ""'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containercleanraws'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L394-L398'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containercleanraws'>
			<code>
				cleanRaws
				<span class='gray'>(keepBetween?)</span>
			</code>
		</a>
	</h3>

	<p>Clear the code style properties for the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>keepBetween</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
				
			</td>
			<td class='col-6'>Keep the raws.between symbols.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>node.raws.before  <span class="hljs-comment">//=&gt; ' '</span>
node.cleanRaws()
node.raws.before  <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerclone'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L183-L189'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerclone'>
			<code>
				clone
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns an exact clone of the node.</p>
<p>The resulting cloned node and its (cloned) children will retain
code style properties.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Clone of the node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.raws.before    <span class="hljs-comment">//=&gt; "\n  "</span>
<span class="hljs-keyword">const</span> cloned = decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })
cloned.raws.before  <span class="hljs-comment">//=&gt; "\n  "</span>
cloned.toString()   <span class="hljs-comment">//=&gt; -moz-transform: scale(0)</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containercloneafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L216-L220'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containercloneafter'>
			<code>
				cloneAfter
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
after the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='containerclonebefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L202-L206'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerclonebefore'>
			<code>
				cloneBefore
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
before the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Mew properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containereach'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L63-L89'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containereach'>
			<code>
				each
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Iterates through the container’s immediate children,
calling <code>callback</code> for each child.</p>
<p>Returning <code>false</code> in the callback will break iteration.</p>
<p>This method only iterates through the container’s immediate children.
If you need to recursively iterate through all the container’s descendant
nodes, use <a href="#containerwalk">Container#walk</a>.</p>
<p>Unlike the for <code>{}</code>-cycle or <code>Array#forEach</code> this iterator is safe
if you are mutating the array of child nodes during iteration.
PostCSS will adjust the current index to match the mutations.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { color: black; z-index: 1 }'</span>)
<span class="hljs-keyword">const</span> rule = root.first

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> decl <span class="hljs-keyword">of</span> rule.nodes) {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Cycle will be infinite, because cloneBefore moves the current node</span>
  <span class="hljs-comment">// to the next index</span>
}

rule.each(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Will be executed only for color and z-index</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containererror'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L89-L95'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containererror'>
			<code>
				error
				<span class='gray'>(message, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>CssSyntaxError</code> instance containing the original position
of the node in the source, showing line and column numbers and also
a small excerpt to facilitate debugging.</p>
<p>If present, an input source map will be used to get the original position
of the source, even from a previous compilation step
(e.g., from Sass compilation).</p>
<p>This method produces very useful error messages.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>message</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Error description.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this error.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#csssyntaxerror">CssSyntaxError</a></code>
	:
	<span class='force-inline'>Error object to throw it.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (!variables[name]) {
  <span class="hljs-keyword">throw</span> decl.error(<span class="hljs-string">'Unknown variable '</span> + name, { <span class="hljs-attr">word</span>: name })
  <span class="hljs-comment">// CssSyntaxError: postcss-vars:a.sass:4:3: Unknown variable $black</span>
  <span class="hljs-comment">//   color: $black</span>
  <span class="hljs-comment">// a</span>
  <span class="hljs-comment">//          ^</span>
  <span class="hljs-comment">//   background: white</span>
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerevery'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L539-L541'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerevery'>
			<code>
				every
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code>
for all of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is every child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> noPrefixes = rule.every(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] !== <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerfirst'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L582-L585'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerfirst'>
			<code>
				first
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s first child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.first === rules.nodes[<span class="hljs-number">0</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerindex'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L568-L572'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerindex'>
			<code>
				index
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>child</code>’s index within the <a href="Container#nodes">Container#nodes</a> array.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Child of the current container.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
	:
	<span class='force-inline'>Child index.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.index( rule.nodes[<span class="hljs-number">2</span>] ) <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerinsertafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L414-L431'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerinsertafter'>
			<code>
				insertAfter
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='containerinsertbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L386-L404'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerinsertbefore'>
			<code>
				insertBefore
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.insertBefore(decl, decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop }))</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerlast'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L595-L598'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerlast'>
			<code>
				last
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s last child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.last === rule.nodes[rule.nodes.length - <span class="hljs-number">1</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containernext'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L260-L264'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containernext'>
			<code>
				next
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the next child of the node’s parent.
Returns <code>undefined</code> if the current node is the last child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Next node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (comment.text === <span class="hljs-string">'delete next'</span>) {
  <span class="hljs-keyword">const</span> next = comment.next()
  <span class="hljs-keyword">if</span> (next) {
    next.remove()
  }
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerprepend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L353-L366'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerprepend'>
			<code>
				prepend
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the start of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.prepend(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerprev'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L278-L282'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerprev'>
			<code>
				prev
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the previous child of the node’s parent.
Returns <code>undefined</code> if the current node is the first child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Previous node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> annotation = decl.prev()
<span class="hljs-keyword">if</span> (annotation.type === <span class="hljs-string">'comment'</span>) {
  readAnnotation(annotation.text)
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerraw'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L363-L366'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerraw'>
			<code>
				raw
				<span class='gray'>(prop, defaultType?)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="Node#raws">Node#raws</a> value. If the node is missing
the code style property (because the node was manually built or cloned),
PostCSS will try to autodetect the code style property by looking
at other nodes in the tree.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Name of code style property.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>defaultType</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Name of default value, it can be missed
if the value is the same as prop.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Code style value.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { background: white }'</span>)
root.nodes[<span class="hljs-number">0</span>].append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raws.before   <span class="hljs-comment">//=&gt; undefined</span>
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raw(<span class="hljs-string">'before'</span>) <span class="hljs-comment">//=&gt; ' '</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerremove'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L139-L145'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerremove'>
			<code>
				remove
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes the node from its parent and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Node to make calls chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (decl.prop.match(<span class="hljs-regexp">/^-webkit-/</span>)) {
  decl.remove()
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerremoveall'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L475-L482'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerremoveall'>
			<code>
				removeAll
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes all children from the container
and cleans their parent properties.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.removeAll()
rule.nodes.length <span class="hljs-comment">//=&gt; 0</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerremovechild'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L447-L463'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerremovechild'>
			<code>
				removeChild
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Removes node from the container and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.nodes.length  <span class="hljs-comment">//=&gt; 5</span>
rule.removeChild(decl)
rule.nodes.length  <span class="hljs-comment">//=&gt; 4</span>
decl.parent        <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerreplacevalues'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L510-L526'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerreplacevalues'>
			<code>
				replaceValues
				<span class='gray'>(pattern, opts, callback)</span>
			</code>
		</a>
	</h3>

	<p>Passes all declaration values within the container that match pattern
through callback, replacing those values with the returned result
of callback.</p>
<p>This method is useful if you are using a custom unit or function
and need to iterate through all values.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>pattern</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)
				
			</td>
			<td class='col-6'>Replace pattern.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-6'>Options to speed up the search.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.props</td>
  <td class="col-2 quiet">
    (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)
    
  </td>
  <td class='col-8'>An array of property names.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.fast</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>String that’s used to narrow down
values and speed up the regexp search.
</td>
</tr>


		
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
				
			</td>
			<td class='col-6'>String to replace pattern or callback
that returns a new value. The callback
will receive the same arguments
as those passed to a function parameter
of 
<code>String#replace</code>
.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.replaceValues(<span class="hljs-regexp">/\d+rem/</span>, { <span class="hljs-attr">fast</span>: <span class="hljs-string">'rem'</span> }, string =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-number">15</span> * <span class="hljs-built_in">parseInt</span>(string) + <span class="hljs-string">'px'</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerreplacewith'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L234-L244'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerreplacewith'>
			<code>
				replaceWith
				<span class='gray'>(nodes)</span>
			</code>
		</a>
	</h3>

	<p>Inserts node(s) before the current node and removes the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>nodes</code></td>
			<td class='col-3 quiet'>
				...<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Mode(s) to replace current one.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Current node to methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (atrule.name === <span class="hljs-string">'mixin'</span>) {
  atrule.replaceWith(mixinRules[atrule.params])
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L376-L380'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerroot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Finds the Root instance of the node’s tree.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>Root parent.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">0</span>].root() === root</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containersome'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L554-L556'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containersome'>
			<code>
				some
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code> for (at least) one
of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is some child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> hasPrefix = rule.some(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] === <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containertostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L158-L165'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containertostring'>
			<code>
				toString
				<span class='gray'>(stringifier = stringify)</span>
			</code>
		</a>
	</h3>

	<p>Returns a CSS string representing the node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>stringifier</code></td>
			<td class='col-3 quiet'>
				(<a href="#stringifier">stringifier</a> | <a href="#syntax">syntax</a>)?
				
				= <code>stringify</code>
			</td>
			<td class='col-6'>A syntax to use
in string generation.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>CSS string of this node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.rule({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> }).toString() <span class="hljs-comment">//=&gt; "a {}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwalk'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L110-L124'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwalk'>
			<code>
				walk
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each node.</p>
<p>Like container.each(), this method is safe to use
if you are mutating arrays during iteration.</p>
<p>If you only need to iterate through the container’s immediate children,
use <a href="#containereach">Container#each</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walk(<span class="hljs-function"><span class="hljs-params">node</span> =&gt;</span> {
  <span class="hljs-comment">// Traverses all descendant nodes.</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwalkatrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L255-L276'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwalkatrules'>
			<code>
				walkAtRules
				<span class='gray'>(name?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each at-rule node.</p>
<p>If you pass a filter, iteration will only happen over at-rules
that have matching names.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>name</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter at-rules by name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkAtRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  <span class="hljs-keyword">if</span> (isOld(rule.name)) rule.remove()
})

<span class="hljs-keyword">let</span> first = <span class="hljs-literal">false</span>
root.walkAtRules(<span class="hljs-string">'charset'</span>, rule =&gt; {
  <span class="hljs-keyword">if</span> (!first) {
    first = <span class="hljs-literal">true</span>
  } <span class="hljs-keyword">else</span> {
    rule.remove()
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwalkcomments'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L294-L300'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwalkcomments'>
			<code>
				walkComments
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each comment node.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkComments(<span class="hljs-function"><span class="hljs-params">comment</span> =&gt;</span> {
  comment.remove()
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwalkdecls'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L155-L176'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwalkdecls'>
			<code>
				walkDecls
				<span class='gray'>(prop?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each declaration node.</p>
<p>If you pass a filter, iteration will only happen over declarations
with matching properties.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter declarations by property name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkDecls(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  checkPropertySupport(decl.prop)
})

root.walkDecls(<span class="hljs-string">'border-radius'</span>, decl =&gt; {
  decl.remove()
})

root.walkDecls(<span class="hljs-regexp">/^background/</span>, decl =&gt; {
  decl.value = takeFirstColorFromGradient(decl.value)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwalkrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L201-L223'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwalkrules'>
			<code>
				walkRules
				<span class='gray'>(selector?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each rule node.</p>
<p>If you pass a filter, iteration will only happen over rules
with matching selectors.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>selector</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter rules by selector.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> selectors = []
root.walkRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  selectors.push(rule.selector)
})
<span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Your CSS uses <span class="hljs-subst">${ selectors.length }</span> selectors`</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='containerwarn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L122-L126'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#containerwarn'>
			<code>
				warn
				<span class='gray'>(result, text, opts?)</span>
			</code>
		</a>
	</h3>

	<p>This method is provided as a convenience wrapper for <a href="#resultwarn">Result#warn</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'>The 
<a href="#result">Result</a>
 instance
that will receive the warning.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Options
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this warning.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#warning">Warning</a></code>
	:
	<span class='force-inline'>Created warning object.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> plugin = postcss.plugin(<span class="hljs-string">'postcss-deprecated'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    root.walkDecls(<span class="hljs-string">'bad'</span>, decl =&gt; {
      decl.warn(result, <span class="hljs-string">'Deprecated property bad'</span>)
    })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='csssyntaxerror'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error">Error</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L33-L243'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerror'>
			<code>
				CssSyntaxError
				<span class='gray'>(message, line?, column?, source?, file?, plugin?)</span>
			</code>
		</a>
	</h3>

	<p>The CSS parser throws this error for broken CSS.</p>
<p>Custom parsers can throw this error for broken custom syntax using
the <a href="#nodeerror">Node#error</a> method.</p>
<p>PostCSS will use the input source map to detect the original error location.
If you wrote a Sass file, compiled it to CSS and then parsed it with PostCSS,
PostCSS will show the original position in the Sass file.</p>
<p>If you need the position in the PostCSS input
(e.g., to debug the previous compiler), use <code>error.input.file</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>message</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Error message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>line</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?
				
			</td>
			<td class='col-6'>Source line of the error.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>column</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?
				
			</td>
			<td class='col-6'>Source column of the error.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>source</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Source code of the broken file.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>file</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Absolute path to the broken file.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>plugin</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>PostCSS plugin name, if error came from plugin.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-comment">// Catching and checking syntax error</span>
<span class="hljs-keyword">try</span> {
  postcss.parse(<span class="hljs-string">'a{'</span>)
} <span class="hljs-keyword">catch</span> (error) {
  <span class="hljs-keyword">if</span> (error.name === <span class="hljs-string">'CssSyntaxError'</span>) {
    error <span class="hljs-comment">//=&gt; CssSyntaxError</span>
  }
}</code></pre>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-comment">// Raising error from plugin</span>
<span class="hljs-keyword">throw</span> node.error(<span class="hljs-string">'Unknown variable'</span>, { <span class="hljs-attr">plugin</span>: <span class="hljs-string">'postcss-vars'</span> })</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='csssyntaxerrorname'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L58-L58'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorname'>
			<code>
				name
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Always equal to <code>'CssSyntaxError'</code>. You should always check error type
by <code>error.name === 'CssSyntaxError'</code>
instead of <code>error instanceof CssSyntaxError</code>,
because npm could have several PostCSS versions.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (error.name === <span class="hljs-string">'CssSyntaxError'</span>) {
  error <span class="hljs-comment">//=&gt; CssSyntaxError</span>
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorreason'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L67-L67'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorreason'>
			<code>
				reason
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Error message.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.message <span class="hljs-comment">//=&gt; 'Unclosed block'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorfile'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L79-L79'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorfile'>
			<code>
				file
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Absolute path to the broken file.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.file       <span class="hljs-comment">//=&gt; 'a.sass'</span>
error.input.file <span class="hljs-comment">//=&gt; 'a.css'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorsource'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L91-L91'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorsource'>
			<code>
				source
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Source code of the broken file.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.source       <span class="hljs-comment">//=&gt; 'a { b {} }'</span>
error.input.column <span class="hljs-comment">//=&gt; 'a b { }'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorplugin'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L102-L102'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorplugin'>
			<code>
				plugin
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Plugin name, if error came from plugin.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.plugin <span class="hljs-comment">//=&gt; 'postcss-vars'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorline'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L114-L114'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorline'>
			<code>
				line
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Source line of the error.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.line       <span class="hljs-comment">//=&gt; 2</span>
error.input.line <span class="hljs-comment">//=&gt; 4</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorcolumn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L124-L124'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorcolumn'>
			<code>
				column
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Source column of the error.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.column       <span class="hljs-comment">//=&gt; 1</span>
error.input.column <span class="hljs-comment">//=&gt; 4</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrormessage'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L144-L144'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrormessage'>
			<code>
				message
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Full error text in the GNU error format
with plugin, file, line and column.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.message <span class="hljs-comment">//=&gt; 'a.css:1:1: Unclosed block'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrorshowsourcecode'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L173-L211'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrorshowsourcecode'>
			<code>
				showSourceCode
				<span class='gray'>(color?)</span>
			</code>
		</a>
	</h3>

	<p>Returns a few lines of CSS source that caused the error.</p>
<p>If the CSS has an input source map without <code>sourceContent</code>,
this method will return an empty string.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>color</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
				
			</td>
			<td class='col-6'>Whether arrow will be colored red by terminal
color codes. By default, PostCSS will detect
color support by 
<code>process.stdout.isTTY</code>

and 
<code>process.env.NODE_DISABLE_COLORS</code>
.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Few lines of CSS source that caused the error.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.showSourceCode() <span class="hljs-comment">//=&gt; "  4 | }</span>
                       <span class="hljs-comment">//      5 | a {</span>
                       <span class="hljs-comment">//    &gt; 6 |   bad</span>
                       <span class="hljs-comment">//        |   ^</span>
                       <span class="hljs-comment">//      7 | }</span>
                       <span class="hljs-comment">//      8 | b {"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='csssyntaxerrortostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/css-syntax-error.js#L223-L229'>
			<span>lib/css-syntax-error.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#csssyntaxerrortostring'>
			<code>
				toString
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns error position, message and source code of the broken part.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Error position, message and source code.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>error.toString() <span class="hljs-comment">//=&gt; "CssSyntaxError: app.css:1:1: Unclosed block</span>
                 <span class="hljs-comment">//    &gt; 1 | a {</span>
                 <span class="hljs-comment">//        | ^"</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='declaration'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#node">Node</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/declaration.js#L14-L75'>
			<span>lib/declaration.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declaration'>
			<code>
				Declaration
				<span class='gray'>(defaults)</span>
			</code>
		</a>
	</h3>

	<p>Represents a CSS declaration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { color: black }'</span>)
<span class="hljs-keyword">const</span> decl = root.first.first
decl.type       <span class="hljs-comment">//=&gt; 'decl'</span>
decl.toString() <span class="hljs-comment">//=&gt; ' color: black'</span></code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='declarationafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L313-L316'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationafter'>
			<code>
				after
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertAfter(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.after(<span class="hljs-string">'color: black'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L296-L299'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationbefore'>
			<code>
				before
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertBefore(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.before(<span class="hljs-string">'content: ""'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationcleanraws'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L394-L398'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationcleanraws'>
			<code>
				cleanRaws
				<span class='gray'>(keepBetween?)</span>
			</code>
		</a>
	</h3>

	<p>Clear the code style properties for the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>keepBetween</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
				
			</td>
			<td class='col-6'>Keep the raws.between symbols.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>node.raws.before  <span class="hljs-comment">//=&gt; ' '</span>
node.cleanRaws()
node.raws.before  <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationclone'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L183-L189'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationclone'>
			<code>
				clone
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns an exact clone of the node.</p>
<p>The resulting cloned node and its (cloned) children will retain
code style properties.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Clone of the node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.raws.before    <span class="hljs-comment">//=&gt; "\n  "</span>
<span class="hljs-keyword">const</span> cloned = decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })
cloned.raws.before  <span class="hljs-comment">//=&gt; "\n  "</span>
cloned.toString()   <span class="hljs-comment">//=&gt; -moz-transform: scale(0)</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationcloneafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L216-L220'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationcloneafter'>
			<code>
				cloneAfter
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
after the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='declarationclonebefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L202-L206'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationclonebefore'>
			<code>
				cloneBefore
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
before the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Mew properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationerror'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L89-L95'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationerror'>
			<code>
				error
				<span class='gray'>(message, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>CssSyntaxError</code> instance containing the original position
of the node in the source, showing line and column numbers and also
a small excerpt to facilitate debugging.</p>
<p>If present, an input source map will be used to get the original position
of the source, even from a previous compilation step
(e.g., from Sass compilation).</p>
<p>This method produces very useful error messages.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>message</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Error description.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this error.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#csssyntaxerror">CssSyntaxError</a></code>
	:
	<span class='force-inline'>Error object to throw it.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (!variables[name]) {
  <span class="hljs-keyword">throw</span> decl.error(<span class="hljs-string">'Unknown variable '</span> + name, { <span class="hljs-attr">word</span>: name })
  <span class="hljs-comment">// CssSyntaxError: postcss-vars:a.sass:4:3: Unknown variable $black</span>
  <span class="hljs-comment">//   color: $black</span>
  <span class="hljs-comment">// a</span>
  <span class="hljs-comment">//          ^</span>
  <span class="hljs-comment">//   background: white</span>
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationnext'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L260-L264'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationnext'>
			<code>
				next
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the next child of the node’s parent.
Returns <code>undefined</code> if the current node is the last child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Next node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (comment.text === <span class="hljs-string">'delete next'</span>) {
  <span class="hljs-keyword">const</span> next = comment.next()
  <span class="hljs-keyword">if</span> (next) {
    next.remove()
  }
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationprev'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L278-L282'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationprev'>
			<code>
				prev
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the previous child of the node’s parent.
Returns <code>undefined</code> if the current node is the first child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Previous node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> annotation = decl.prev()
<span class="hljs-keyword">if</span> (annotation.type === <span class="hljs-string">'comment'</span>) {
  readAnnotation(annotation.text)
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationraw'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L363-L366'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationraw'>
			<code>
				raw
				<span class='gray'>(prop, defaultType?)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="Node#raws">Node#raws</a> value. If the node is missing
the code style property (because the node was manually built or cloned),
PostCSS will try to autodetect the code style property by looking
at other nodes in the tree.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Name of code style property.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>defaultType</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Name of default value, it can be missed
if the value is the same as prop.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Code style value.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { background: white }'</span>)
root.nodes[<span class="hljs-number">0</span>].append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raws.before   <span class="hljs-comment">//=&gt; undefined</span>
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raw(<span class="hljs-string">'before'</span>) <span class="hljs-comment">//=&gt; ' '</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationremove'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L139-L145'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationremove'>
			<code>
				remove
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes the node from its parent and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Node to make calls chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (decl.prop.match(<span class="hljs-regexp">/^-webkit-/</span>)) {
  decl.remove()
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationreplacewith'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L234-L244'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationreplacewith'>
			<code>
				replaceWith
				<span class='gray'>(nodes)</span>
			</code>
		</a>
	</h3>

	<p>Inserts node(s) before the current node and removes the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>nodes</code></td>
			<td class='col-3 quiet'>
				...<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Mode(s) to replace current one.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Current node to methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (atrule.name === <span class="hljs-string">'mixin'</span>) {
  atrule.replaceWith(mixinRules[atrule.params])
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L376-L380'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationroot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Finds the Root instance of the node’s tree.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>Root parent.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">0</span>].root() === root</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationtostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L158-L165'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationtostring'>
			<code>
				toString
				<span class='gray'>(stringifier = stringify)</span>
			</code>
		</a>
	</h3>

	<p>Returns a CSS string representing the node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>stringifier</code></td>
			<td class='col-3 quiet'>
				(<a href="#stringifier">stringifier</a> | <a href="#syntax">syntax</a>)?
				
				= <code>stringify</code>
			</td>
			<td class='col-6'>A syntax to use
in string generation.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>CSS string of this node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.rule({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> }).toString() <span class="hljs-comment">//=&gt; "a {}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='declarationwarn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L122-L126'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#declarationwarn'>
			<code>
				warn
				<span class='gray'>(result, text, opts?)</span>
			</code>
		</a>
	</h3>

	<p>This method is provided as a convenience wrapper for <a href="#resultwarn">Result#warn</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'>The 
<a href="#result">Result</a>
 instance
that will receive the warning.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Options
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this warning.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#warning">Warning</a></code>
	:
	<span class='force-inline'>Created warning object.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> plugin = postcss.plugin(<span class="hljs-string">'postcss-deprecated'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    root.walkDecls(<span class="hljs-string">'bad'</span>, decl =&gt; {
      decl.warn(result, <span class="hljs-string">'Deprecated property bad'</span>)
    })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='input'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L14-L167'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#input'>
			<code>
				Input
				<span class='gray'>(css, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Represents the source CSS.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Input CSS source.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'><a href="#processorprocess">Processor#process</a>
 options.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root  = postcss.parse(css, { <span class="hljs-attr">from</span>: file })
<span class="hljs-keyword">const</span> input = root.source.input</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='inputcss'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L33-L33'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputcss'>
			<code>
				css
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Input CSS source</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> input = postcss.parse(<span class="hljs-string">'a{}'</span>, { <span class="hljs-attr">from</span>: file }).input
input.css <span class="hljs-comment">//=&gt; "a{}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='inputfile'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L54-L54'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputfile'>
			<code>
				file
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The absolute path to the CSS source file defined
with the <code>from</code> option.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(css, { <span class="hljs-attr">from</span>: <span class="hljs-string">'a.css'</span> })
root.source.input.file <span class="hljs-comment">//=&gt; '/home/<USER>/a.css'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='inputmap'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L71-L71'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputmap'>
			<code>
				map
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The input source map passed from a compilation step before PostCSS
(for example, from Sass compiler).</p>

	
	<p>
		Type:
		<a href="#previousmap">PreviousMap</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.source.input.map.consumer().sources <span class="hljs-comment">//=&gt; ['a.sass']</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='inputid'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L88-L88'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputid'>
			<code>
				id
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The unique ID of the CSS source. It will be created if <code>from</code> option
is not provided (because PostCSS does not know the file path).</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(css)
root.source.input.file <span class="hljs-comment">//=&gt; undefined</span>
root.source.input.id   <span class="hljs-comment">//=&gt; "&lt;input css 8LZeVF&gt;"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='inputorigin'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L125-L142'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputorigin'>
			<code>
				origin
				<span class='gray'>(line, column)</span>
			</code>
		</a>
	</h3>

	<p>Reads the input source map and returns a symbol position
in the input source (e.g., in a Sass file that was compiled
to CSS before being passed to PostCSS).</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>line</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-6'>Line in input CSS.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>column</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-6'>Column in input CSS.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#fileposition">filePosition</a></code>
	:
	<span class='force-inline'>Position in input source.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.source.input.origin(<span class="hljs-number">1</span>, <span class="hljs-number">1</span>) <span class="hljs-comment">//=&gt; { file: 'a.css', line: 3, column: 1 }</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='inputfrom'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L164-L166'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#inputfrom'>
			<code>
				from
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The CSS source identifier. Contains <a href="#inputfile">Input#file</a> if the user
set the <code>from</code> option, or <a href="#inputid">Input#id</a> if they did not.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(css, { <span class="hljs-attr">from</span>: <span class="hljs-string">'a.css'</span> })
root.source.input.from <span class="hljs-comment">//=&gt; "/home/<USER>/a.css"</span>

<span class="hljs-keyword">const</span> root = postcss.parse(css)
root.source.input.from <span class="hljs-comment">//=&gt; "&lt;input css 1&gt;"</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='lazyresult'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L44-L427'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresult'>
			<code>
				LazyResult
				<span class='gray'>(processor, css, opts)</span>
			</code>
		</a>
	</h3>

	<p>A Promise proxy for the result of PostCSS transformations.</p>
<p>A <code>LazyResult</code> instance is returned by <a href="#processorprocess">Processor#process</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>processor</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> lazy = postcss([autoprefixer]).process(css)</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='lazyresultprocessor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L81-L83'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultprocessor'>
			<code>
				processor
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="#processor">Processor</a> instance, which will be used
for CSS transformations.</p>

	
	<p>
		Type:
		<a href="#processor">Processor</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultopts'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L90-L92'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultopts'>
			<code>
				opts
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Options from the <a href="#processorprocess">Processor#process</a> call.</p>

	
	<p>
		Type:
		<a href="#processoptions">processOptions</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultcss'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L106-L108'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultcss'>
			<code>
				css
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous plugins, converts <code>Root</code>
to a CSS string and returns <a href="#resultcss">Result#css</a>.</p>
<p>This property will only work with synchronous plugins.
If the processor contains any asynchronous plugins
it will throw an error. This is why this method is only
for debug purpose, you should always use <a href="#lazyresultthen">LazyResult#then</a>.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultcontent'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L122-L124'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultcontent'>
			<code>
				content
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>An alias for the <code>css</code> property. Use it with syntaxes
that generate non-CSS output.</p>
<p>This property will only work with synchronous plugins.
If the processor contains any asynchronous plugins
it will throw an error. This is why this method is only
for debug purpose, you should always use <a href="#lazyresultthen">LazyResult#then</a>.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultmap'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L138-L140'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultmap'>
			<code>
				map
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous plugins
and returns <a href="#resultmap">Result#map</a>.</p>
<p>This property will only work with synchronous plugins.
If the processor contains any asynchronous plugins
it will throw an error. This is why this method is only
for debug purpose, you should always use <a href="#lazyresultthen">LazyResult#then</a>.</p>

	
	<p>
		Type:
		SourceMapGenerator
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L155-L157'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultroot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous plugins
and returns <a href="#resultroot">Result#root</a>.</p>
<p>This property will only work with synchronous plugins. If the processor
contains any asynchronous plugins it will throw an error.</p>
<p>This is why this method is only for debug purpose,
you should always use <a href="#lazyresultthen">LazyResult#then</a>.</p>

	
	<p>
		Type:
		<a href="#root">Root</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultmessages'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L172-L174'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultmessages'>
			<code>
				messages
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous plugins
and returns <a href="#resultmessages">Result#messages</a>.</p>
<p>This property will only work with synchronous plugins. If the processor
contains any asynchronous plugins it will throw an error.</p>
<p>This is why this method is only for debug purpose,
you should always use <a href="#lazyresultthen">LazyResult#then</a>.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#message">Message</a>>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='lazyresultwarnings'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L182-L184'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultwarnings'>
			<code>
				warnings
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous plugins
and calls <a href="Result#warnings()">Result#warnings()</a>.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#warning">Warning</a>></code>
	:
	<span class='force-inline'>Warnings from plugins.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='lazyresulttostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L194-L196'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresulttostring'>
			<code>
				toString
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Alias for the <a href="#lazyresultcss">LazyResult#css</a> property.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Output CSS.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>lazy + <span class="hljs-string">''</span> === lazy.css</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='lazyresultthen'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L216-L227'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultthen'>
			<code>
				then
				<span class='gray'>(onFulfilled, onRejected)</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous and asynchronous plugins
and calls <code>onFulfilled</code> with a Result instance. If a plugin throws
an error, the <code>onRejected</code> callback will be executed.</p>
<p>It implements standard Promise API.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>onFulfilled</code></td>
			<td class='col-3 quiet'>
				<a href="#onfulfilled">onFulfilled</a>
				
			</td>
			<td class='col-6'>Callback will be executed
when all plugins will finish work.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>onRejected</code></td>
			<td class='col-3 quiet'>
				<a href="#onrejected">onRejected</a>
				
			</td>
			<td class='col-6'>Callback will be executed on any error.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>
	:
	<span class='force-inline'>Promise API to make queue.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss([autoprefixer]).process(css, { <span class="hljs-attr">from</span>: cssPath }).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.log(result.css)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='lazyresultcatch'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L246-L248'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultcatch'>
			<code>
				catch
				<span class='gray'>(onRejected)</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous and asynchronous plugins
and calls onRejected for each error thrown in any plugin.</p>
<p>It implements standard Promise API.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>onRejected</code></td>
			<td class='col-3 quiet'>
				<a href="#onrejected">onRejected</a>
				
			</td>
			<td class='col-6'>Callback will be executed on any error.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>
	:
	<span class='force-inline'>Promise API to make queue.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss([autoprefixer]).process(css).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.log(result.css)
}).catch(<span class="hljs-function"><span class="hljs-params">error</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.error(error)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='lazyresultfinally'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L266-L268'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#lazyresultfinally'>
			<code>
				finally
				<span class='gray'>(onFinally)</span>
			</code>
		</a>
	</h3>

	<p>Processes input CSS through synchronous and asynchronous plugins
and calls onFinally on any error or when all plugins will finish work.</p>
<p>It implements standard Promise API.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>onFinally</code></td>
			<td class='col-3 quiet'>
				onFinally
				
			</td>
			<td class='col-6'>Callback will be executed on any error or
when all plugins will finish work.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>
	:
	<span class='force-inline'>Promise API to make queue.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss([autoprefixer]).process(css).finally(<span class="hljs-function"><span class="hljs-params">()</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.log(<span class="hljs-string">'processing ended'</span>)
})</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='node'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L35-L564'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#node'>
			<code>
				Node
				<span class='gray'>(defaults = {})</span>
			</code>
		</a>
	</h3>

	<p>All node classes inherit the following common methods.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Value for node properties.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='nodeerror'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L89-L95'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodeerror'>
			<code>
				error
				<span class='gray'>(message, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>CssSyntaxError</code> instance containing the original position
of the node in the source, showing line and column numbers and also
a small excerpt to facilitate debugging.</p>
<p>If present, an input source map will be used to get the original position
of the source, even from a previous compilation step
(e.g., from Sass compilation).</p>
<p>This method produces very useful error messages.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>message</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Error description.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this error.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the error.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#csssyntaxerror">CssSyntaxError</a></code>
	:
	<span class='force-inline'>Error object to throw it.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (!variables[name]) {
  <span class="hljs-keyword">throw</span> decl.error(<span class="hljs-string">'Unknown variable '</span> + name, { <span class="hljs-attr">word</span>: name })
  <span class="hljs-comment">// CssSyntaxError: postcss-vars:a.sass:4:3: Unknown variable $black</span>
  <span class="hljs-comment">//   color: $black</span>
  <span class="hljs-comment">// a</span>
  <span class="hljs-comment">//          ^</span>
  <span class="hljs-comment">//   background: white</span>
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodewarn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L122-L126'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodewarn'>
			<code>
				warn
				<span class='gray'>(result, text, opts?)</span>
			</code>
		</a>
	</h3>

	<p>This method is provided as a convenience wrapper for <a href="#resultwarn">Result#warn</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'>The 
<a href="#result">Result</a>
 instance
that will receive the warning.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Options
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Plugin name that created this warning.
PostCSS will set it automatically.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>A word inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>An index inside a node’s string that should
be highlighted as the source of the warning.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#warning">Warning</a></code>
	:
	<span class='force-inline'>Created warning object.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> plugin = postcss.plugin(<span class="hljs-string">'postcss-deprecated'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    root.walkDecls(<span class="hljs-string">'bad'</span>, decl =&gt; {
      decl.warn(result, <span class="hljs-string">'Deprecated property bad'</span>)
    })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='noderemove'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L139-L145'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#noderemove'>
			<code>
				remove
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes the node from its parent and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Node to make calls chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (decl.prop.match(<span class="hljs-regexp">/^-webkit-/</span>)) {
  decl.remove()
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodetostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L158-L165'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodetostring'>
			<code>
				toString
				<span class='gray'>(stringifier = stringify)</span>
			</code>
		</a>
	</h3>

	<p>Returns a CSS string representing the node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>stringifier</code></td>
			<td class='col-3 quiet'>
				(<a href="#stringifier">stringifier</a> | <a href="#syntax">syntax</a>)?
				
				= <code>stringify</code>
			</td>
			<td class='col-6'>A syntax to use
in string generation.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>CSS string of this node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.rule({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> }).toString() <span class="hljs-comment">//=&gt; "a {}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodeclone'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L183-L189'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodeclone'>
			<code>
				clone
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns an exact clone of the node.</p>
<p>The resulting cloned node and its (cloned) children will retain
code style properties.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Clone of the node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.raws.before    <span class="hljs-comment">//=&gt; "\n  "</span>
<span class="hljs-keyword">const</span> cloned = decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })
cloned.raws.before  <span class="hljs-comment">//=&gt; "\n  "</span>
cloned.toString()   <span class="hljs-comment">//=&gt; -moz-transform: scale(0)</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodeclonebefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L202-L206'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodeclonebefore'>
			<code>
				cloneBefore
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
before the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Mew properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-moz-'</span> + decl.prop })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodecloneafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L216-L220'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodecloneafter'>
			<code>
				cloneAfter
				<span class='gray'>(overrides = {})</span>
			</code>
		</a>
	</h3>

	<p>Shortcut to clone the node and insert the resulting cloned node
after the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>overrides</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>New properties to override in the clone.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>New node.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='nodereplacewith'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L234-L244'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodereplacewith'>
			<code>
				replaceWith
				<span class='gray'>(nodes)</span>
			</code>
		</a>
	</h3>

	<p>Inserts node(s) before the current node and removes the current node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>nodes</code></td>
			<td class='col-3 quiet'>
				...<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Mode(s) to replace current one.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>Current node to methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (atrule.name === <span class="hljs-string">'mixin'</span>) {
  atrule.replaceWith(mixinRules[atrule.params])
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodenext'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L260-L264'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodenext'>
			<code>
				next
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the next child of the node’s parent.
Returns <code>undefined</code> if the current node is the last child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Next node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (comment.text === <span class="hljs-string">'delete next'</span>) {
  <span class="hljs-keyword">const</span> next = comment.next()
  <span class="hljs-keyword">if</span> (next) {
    next.remove()
  }
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodeprev'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L278-L282'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodeprev'>
			<code>
				prev
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns the previous child of the node’s parent.
Returns <code>undefined</code> if the current node is the first child.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Previous node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> annotation = decl.prev()
<span class="hljs-keyword">if</span> (annotation.type === <span class="hljs-string">'comment'</span>) {
  readAnnotation(annotation.text)
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodebefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L296-L299'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodebefore'>
			<code>
				before
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertBefore(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.before(<span class="hljs-string">'content: ""'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodeafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L313-L316'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodeafter'>
			<code>
				after
				<span class='gray'>(add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after current node to current node’s parent.</p>
<p>Just alias for <code>node.parent.insertAfter(node, add)</code>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>decl.after(<span class="hljs-string">'color: black'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='noderaw'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L363-L366'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#noderaw'>
			<code>
				raw
				<span class='gray'>(prop, defaultType?)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="Node#raws">Node#raws</a> value. If the node is missing
the code style property (because the node was manually built or cloned),
PostCSS will try to autodetect the code style property by looking
at other nodes in the tree.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Name of code style property.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>defaultType</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?
				
			</td>
			<td class='col-6'>Name of default value, it can be missed
if the value is the same as prop.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Code style value.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { background: white }'</span>)
root.nodes[<span class="hljs-number">0</span>].append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raws.before   <span class="hljs-comment">//=&gt; undefined</span>
root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">1</span>].raw(<span class="hljs-string">'before'</span>) <span class="hljs-comment">//=&gt; ' '</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='noderoot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L376-L380'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#noderoot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Finds the Root instance of the node’s tree.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>Root parent.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.nodes[<span class="hljs-number">0</span>].nodes[<span class="hljs-number">0</span>].root() === root</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='nodecleanraws'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L394-L398'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#nodecleanraws'>
			<code>
				cleanRaws
				<span class='gray'>(keepBetween?)</span>
			</code>
		</a>
	</h3>

	<p>Clear the code style properties for the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>keepBetween</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?
				
			</td>
			<td class='col-6'>Keep the raws.between symbols.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>node.raws.before  <span class="hljs-comment">//=&gt; ' '</span>
node.cleanRaws()
node.raws.before  <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='previousmap'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/previous-map.js#L24-L140'>
			<span>lib/previous-map.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#previousmap'>
			<code>
				PreviousMap
				<span class='gray'>(css, opts?)</span>
			</code>
		</a>
	</h3>

	<p>Source map information from input CSS.
For example, source map after Sass compiler.</p>
<p>This class will automatically find source map in input CSS or in file system
near input file (according <code>from</code> option).</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Input CSS source.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>?
				
			</td>
			<td class='col-6'><a href="#processorprocess">Processor#process</a>
 options.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(css, { <span class="hljs-attr">from</span>: <span class="hljs-string">'a.sass.css'</span> })
root.input.map <span class="hljs-comment">//=&gt; PreviousMap</span></code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='previousmapinline'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/previous-map.js#L36-L36'>
			<span>lib/previous-map.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#previousmapinline'>
			<code>
				inline
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Was source map inlined by data-uri to input CSS.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	

	

	

	

	
</section>

  
    <section id='previousmapconsumer'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/previous-map.js#L52-L57'>
			<span>lib/previous-map.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#previousmapconsumer'>
			<code>
				consumer
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Create a instance of <code>SourceMapGenerator</code> class
from the <code>source-map</code> library to work with source map information.</p>
<p>It is lazy method, so it will create object only on first call
and then it will use cache.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>SourceMapGenerator</code>
	:
	<span class='force-inline'>Object with source map information.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='previousmapwithcontent'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/previous-map.js#L64-L67'>
			<span>lib/previous-map.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#previousmapwithcontent'>
			<code>
				withContent
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Does source map contains <code>sourcesContent</code> with input source text.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is 
<code>sourcesContent</code>
 present.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='processor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L12-L135'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processor'>
			<code>
				Processor
				<span class='gray'>(plugins)</span>
			</code>
		</a>
	</h3>

	<p>Contains plugins to process CSS. Create one <code>Processor</code> instance,
initialize its plugins, and then use that instance on numerous CSS files.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>plugins</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="#plugin">Plugin</a> | <a href="#pluginfunction">pluginFunction</a>)> | <a href="#processor">Processor</a>)
				
				= <code>[]</code>
			</td>
			<td class='col-6'>PostCSS plugins.
See 
<a href="#processoruse">Processor#use</a>
 for plugin format.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> processor = postcss([autoprefixer, precss])
processor.process(css1).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> <span class="hljs-built_in">console</span>.log(result.css))
processor.process(css2).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> <span class="hljs-built_in">console</span>.log(result.css))</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='processorversion'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L28-L28'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processorversion'>
			<code>
				version
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Current PostCSS version.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (result.processor.version.split(<span class="hljs-string">'.'</span>)[<span class="hljs-number">0</span>] !== <span class="hljs-string">'6'</span>) {
  <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'This plugin works only with PostCSS 6'</span>)
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='processorplugins'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L38-L38'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processorplugins'>
			<code>
				plugins
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Plugins added to this processor.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#pluginfunction">pluginFunction</a>>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> processor = postcss([autoprefixer, precss])
processor.plugins.length <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='processoruse'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L70-L73'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processoruse'>
			<code>
				use
				<span class='gray'>(plugin)</span>
			</code>
		</a>
	</h3>

	<p>Adds a plugin to be used as a CSS processor.</p>
<p>PostCSS plugin can be in 4 formats:</p>
<ul>
<li>A plugin created by <a href="#postcssplugin">postcss.plugin</a> method.</li>
<li>A function. PostCSS will pass the function a @{link Root}
as the first argument and current <a href="#result">Result</a> instance
as the second.</li>
<li>An object with a <code>postcss</code> method. PostCSS will use that method
as described in #2.</li>
<li>Another <a href="#processor">Processor</a> instance. PostCSS will copy plugins
from that instance into this one.</li>
</ul>
<p>Plugins can also be added by passing them as arguments when creating
a <code>postcss</code> instance (see [<code>postcss(plugins)</code>]).</p>
<p>Asynchronous plugins should return a <code>Promise</code> instance.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>plugin</code></td>
			<td class='col-3 quiet'>
				(<a href="#plugin">Plugin</a> | <a href="#pluginfunction">pluginFunction</a> | <a href="#processor">Processor</a>)
				
			</td>
			<td class='col-6'>PostCSS plugin
or 
<a href="#processor">Processor</a>

with plugins.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>Processes</code>
	:
	<span class='force-inline'>Current processor to make methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> processor = postcss()
  .use(autoprefixer)
  .use(precss)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='processorprocess'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L97-L110'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processorprocess'>
			<code>
				process
				<span class='gray'>(css, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Parses source CSS and returns a <a href="#lazyresult">LazyResult</a> Promise proxy.
Because some plugins can be asynchronous it doesn’t make
any transformations. Transformations will be applied
in the <a href="#lazyresult">LazyResult</a> methods.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="#tostring">toString</a> | <a href="#result">Result</a>)
				
			</td>
			<td class='col-6'>String with input CSS or any object
with a 
<code>toString()</code>
 method,
like a Buffer. Optionally, send
a 
<a href="#result">Result</a>
 instance
and the processor will take
the 
<a href="#root">Root</a>
 from it.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#lazyresult">LazyResult</a></code>
	:
	<span class='force-inline'>Promise proxy.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>processor.process(css, { <span class="hljs-attr">from</span>: <span class="hljs-string">'a.css'</span>, <span class="hljs-attr">to</span>: <span class="hljs-string">'a.out.css'</span> })
  .then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> {
     <span class="hljs-built_in">console</span>.log(result.css)
  })</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='result'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L17-L170'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#result'>
			<code>
				Result
				<span class='gray'>(processor, root, opts)</span>
			</code>
		</a>
	</h3>

	<p>Provides the result of the PostCSS transformations.</p>
<p>A Result instance is returned by <a href="#lazyresultthen">LazyResult#then</a>
or <a href="#roottoresult">Root#toResult</a> methods.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>processor</code></td>
			<td class='col-3 quiet'>
				<a href="#processor">Processor</a>
				
			</td>
			<td class='col-6'>Processor used for this transformation.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>root</code></td>
			<td class='col-3 quiet'>
				<a href="#root">Root</a>
				
			</td>
			<td class='col-6'>Root node after all transformations.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>
				
			</td>
			<td class='col-6'>Options from the 
<a href="#processorprocess">Processor#process</a>

or 
<a href="#roottoresult">Root#toResult</a>
.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss([autoprefixer]).process(css).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> {
 <span class="hljs-built_in">console</span>.log(result.css)
})</code></pre>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> result2 = postcss.parse(css).toResult()</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='resultprocessor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L37-L37'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultprocessor'>
			<code>
				processor
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The Processor instance used for this transformation.</p>

	
	<p>
		Type:
		<a href="#processor">Processor</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> plugin <span class="hljs-keyword">of</span> result.processor.plugins) {
  <span class="hljs-keyword">if</span> (plugin.postcssPlugin === <span class="hljs-string">'postcss-bad'</span>) {
    <span class="hljs-keyword">throw</span> <span class="hljs-string">'postcss-good is incompatible with postcss-bad'</span>
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultmessages'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L56-L56'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultmessages'>
			<code>
				messages
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Contains messages from plugins (e.g., warnings or custom messages).
Each message should have type and plugin properties.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#message">Message</a>>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.plugin(<span class="hljs-string">'postcss-min-browser'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    <span class="hljs-keyword">const</span> browsers = detectMinBrowsersByCanIUse(root)
    result.messages.push({
      <span class="hljs-attr">type</span>: <span class="hljs-string">'min-browser'</span>,
      <span class="hljs-attr">plugin</span>: <span class="hljs-string">'postcss-min-browser'</span>,
      browsers
    })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L65-L65'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultroot'>
			<code>
				root
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Root node after all transformations.</p>

	
	<p>
		Type:
		<a href="#root">Root</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.toResult().root === root</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultopts'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L75-L75'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultopts'>
			<code>
				opts
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Options from the <a href="#processorprocess">Processor#process</a> or <a href="#roottoresult">Root#toResult</a> call
that produced this Result instance.</p>

	
	<p>
		Type:
		<a href="#processoptions">processOptions</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.toResult(opts).opts === opts</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultcss'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L84-L84'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultcss'>
			<code>
				css
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>A CSS string representing of <a href="#resultroot">Result#root</a>.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.parse(<span class="hljs-string">'a{}'</span>).toResult().css <span class="hljs-comment">//=&gt; "a{}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultmap'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L99-L99'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultmap'>
			<code>
				map
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>An instance of <code>SourceMapGenerator</code> class from the <code>source-map</code> library,
representing changes to the <a href="#resultroot">Result#root</a> instance.</p>

	
	<p>
		Type:
		SourceMapGenerator
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>result.map.toJSON() <span class="hljs-comment">//=&gt; { version: 3, file: 'a.css', … }</span></code></pre>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (result.map) {
  fs.writeFileSync(result.opts.to + <span class="hljs-string">'.map'</span>, result.map.toString())
}</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resulttostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L110-L112'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resulttostring'>
			<code>
				toString
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns for @{link Result#css} content.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>String representing of 
<a href="#resultroot">Result#root</a>
.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>result + <span class="hljs-string">''</span> === result.css</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultwarn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L130-L141'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultwarn'>
			<code>
				warn
				<span class='gray'>(text, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Creates an instance of <a href="#warning">Warning</a> and adds it
to <a href="#resultmessages">Result#messages</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Warning options.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.node</td>
  <td class="col-2 quiet">
    <a href="#node">Node</a>
    
  </td>
  <td class='col-8'>CSS node that caused the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.word</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Word in CSS source that caused the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.index</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    
  </td>
  <td class='col-8'>Index in CSS node string that caused
the warning.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.plugin</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>Name of the plugin that created
this warning. 
<a href="#resultwarn">Result#warn</a>
 fills
this property automatically.
</td>
</tr>


		
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#warning">Warning</a></code>
	:
	<span class='force-inline'>Created warning.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='resultwarnings'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L154-L156'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultwarnings'>
			<code>
				warnings
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns warnings from plugins. Filters <a href="#warning">Warning</a> instances
from <a href="#resultmessages">Result#messages</a>.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#warning">Warning</a>></code>
	:
	<span class='force-inline'>Warnings from plugins.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>result.warnings().forEach(<span class="hljs-function"><span class="hljs-params">warn</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.warn(warn.toString())
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='resultcontent'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L167-L169'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#resultcontent'>
			<code>
				content
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>An alias for the <a href="#resultcss">Result#css</a> property.
Use it with syntaxes that generate non-CSS output.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>result.css === result.content</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='root'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#container">Container</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/root.js#L13-L116'>
			<span>lib/root.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#root'>
			<code>
				Root
				<span class='gray'>(defaults)</span>
			</code>
		</a>
	</h3>

	<p>Represents a CSS file and contains all its parsed nodes.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a{color:black} b{z-index:2}'</span>)
root.type         <span class="hljs-comment">//=&gt; 'root'</span>
root.nodes.length <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='rootappend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L322-L331'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootappend'>
			<code>
				append
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the end of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.append(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rooteach'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L63-L89'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rooteach'>
			<code>
				each
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Iterates through the container’s immediate children,
calling <code>callback</code> for each child.</p>
<p>Returning <code>false</code> in the callback will break iteration.</p>
<p>This method only iterates through the container’s immediate children.
If you need to recursively iterate through all the container’s descendant
nodes, use <a href="#containerwalk">Container#walk</a>.</p>
<p>Unlike the for <code>{}</code>-cycle or <code>Array#forEach</code> this iterator is safe
if you are mutating the array of child nodes during iteration.
PostCSS will adjust the current index to match the mutations.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { color: black; z-index: 1 }'</span>)
<span class="hljs-keyword">const</span> rule = root.first

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> decl <span class="hljs-keyword">of</span> rule.nodes) {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Cycle will be infinite, because cloneBefore moves the current node</span>
  <span class="hljs-comment">// to the next index</span>
}

rule.each(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Will be executed only for color and z-index</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootevery'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L539-L541'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootevery'>
			<code>
				every
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code>
for all of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is every child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> noPrefixes = rule.every(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] !== <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootfirst'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L582-L585'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootfirst'>
			<code>
				first
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s first child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.first === rules.nodes[<span class="hljs-number">0</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootindex'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L568-L572'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootindex'>
			<code>
				index
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>child</code>’s index within the <a href="Container#nodes">Container#nodes</a> array.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Child of the current container.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
	:
	<span class='force-inline'>Child index.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.index( rule.nodes[<span class="hljs-number">2</span>] ) <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootinsertafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L414-L431'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootinsertafter'>
			<code>
				insertAfter
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='rootinsertbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L386-L404'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootinsertbefore'>
			<code>
				insertBefore
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.insertBefore(decl, decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop }))</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootlast'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L595-L598'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootlast'>
			<code>
				last
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s last child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.last === rule.nodes[rule.nodes.length - <span class="hljs-number">1</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rooton'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/root.js#L95-L99'>
			<span>lib/root.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rooton'>
			<code>
				on
				<span class='gray'>(event, visitor?, type?)</span>
			</code>
		</a>
	</h3>

	<p>Add visitor for next PostCSS walk.</p>
<p>Visitor subscribes for events. Each event contain node type (<code>atrule</code>,
<code>rule</code>, <code>decl</code>, <code>comment</code>) and phase (<code>enter</code>, <code>exit</code>) separated with dot.
The default phase is <code>enter</code>. As result possible events could be like
<code>comment.enter</code>, <code>decl.exit</code> or <code>rule</code> (equal to <code>rule.enter</code>).</p>
<p>PostCSS will walk through CSS AST and call visitor according current node.
Visitor will receive node and node’s index.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>event</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>visitor</code></td>
			<td class='col-3 quiet'>
				<a href="#visitor">visitor</a>?
				
			</td>
			<td class='col-6'>Function receives node and index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>type</code></td>
			<td class='col-3 quiet'>
				visitingEvent?
				
			</td>
			<td class='col-6'>The type of the node and phase.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>css.on(<span class="hljs-string">'decl'</span>, (node, index) =&gt; {
  <span class="hljs-keyword">if</span> (node.prop === <span class="hljs-string">'will-change'</span>) {
    node.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'backface-visibility'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'hidden'</span> })
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootprepend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L353-L366'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootprepend'>
			<code>
				prepend
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the start of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.prepend(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootremoveall'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L475-L482'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootremoveall'>
			<code>
				removeAll
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes all children from the container
and cleans their parent properties.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.removeAll()
rule.nodes.length <span class="hljs-comment">//=&gt; 0</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootremovechild'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L447-L463'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootremovechild'>
			<code>
				removeChild
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Removes node from the container and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.nodes.length  <span class="hljs-comment">//=&gt; 5</span>
rule.removeChild(decl)
rule.nodes.length  <span class="hljs-comment">//=&gt; 4</span>
decl.parent        <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootreplacevalues'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L510-L526'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootreplacevalues'>
			<code>
				replaceValues
				<span class='gray'>(pattern, opts, callback)</span>
			</code>
		</a>
	</h3>

	<p>Passes all declaration values within the container that match pattern
through callback, replacing those values with the returned result
of callback.</p>
<p>This method is useful if you are using a custom unit or function
and need to iterate through all values.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>pattern</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)
				
			</td>
			<td class='col-6'>Replace pattern.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-6'>Options to speed up the search.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.props</td>
  <td class="col-2 quiet">
    (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)
    
  </td>
  <td class='col-8'>An array of property names.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.fast</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>String that’s used to narrow down
values and speed up the regexp search.
</td>
</tr>


		
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
				
			</td>
			<td class='col-6'>String to replace pattern or callback
that returns a new value. The callback
will receive the same arguments
as those passed to a function parameter
of 
<code>String#replace</code>
.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.replaceValues(<span class="hljs-regexp">/\d+rem/</span>, { <span class="hljs-attr">fast</span>: <span class="hljs-string">'rem'</span> }, string =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-number">15</span> * <span class="hljs-built_in">parseInt</span>(string) + <span class="hljs-string">'px'</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootsome'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L554-L556'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootsome'>
			<code>
				some
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code> for (at least) one
of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is some child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> hasPrefix = rule.some(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] === <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='roottoresult'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/root.js#L64-L70'>
			<span>lib/root.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#roottoresult'>
			<code>
				toResult
				<span class='gray'>(opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Returns a <a href="#result">Result</a> instance representing the root’s CSS.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Options with only 
<code>to</code>
 and 
<code>map</code>
 keys.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#result">Result</a></code>
	:
	<span class='force-inline'>Result with current root’s CSS.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root1 = postcss.parse(css1, { <span class="hljs-attr">from</span>: <span class="hljs-string">'a.css'</span> })
<span class="hljs-keyword">const</span> root2 = postcss.parse(css2, { <span class="hljs-attr">from</span>: <span class="hljs-string">'b.css'</span> })
root1.append(root2)
<span class="hljs-keyword">const</span> result = root1.toResult({ <span class="hljs-attr">to</span>: <span class="hljs-string">'all.css'</span>, <span class="hljs-attr">map</span>: <span class="hljs-literal">true</span> })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootwalk'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L110-L124'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootwalk'>
			<code>
				walk
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each node.</p>
<p>Like container.each(), this method is safe to use
if you are mutating arrays during iteration.</p>
<p>If you only need to iterate through the container’s immediate children,
use <a href="#containereach">Container#each</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walk(<span class="hljs-function"><span class="hljs-params">node</span> =&gt;</span> {
  <span class="hljs-comment">// Traverses all descendant nodes.</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootwalkatrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L255-L276'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootwalkatrules'>
			<code>
				walkAtRules
				<span class='gray'>(name?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each at-rule node.</p>
<p>If you pass a filter, iteration will only happen over at-rules
that have matching names.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>name</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter at-rules by name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkAtRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  <span class="hljs-keyword">if</span> (isOld(rule.name)) rule.remove()
})

<span class="hljs-keyword">let</span> first = <span class="hljs-literal">false</span>
root.walkAtRules(<span class="hljs-string">'charset'</span>, rule =&gt; {
  <span class="hljs-keyword">if</span> (!first) {
    first = <span class="hljs-literal">true</span>
  } <span class="hljs-keyword">else</span> {
    rule.remove()
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootwalkcomments'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L294-L300'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootwalkcomments'>
			<code>
				walkComments
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each comment node.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkComments(<span class="hljs-function"><span class="hljs-params">comment</span> =&gt;</span> {
  comment.remove()
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootwalkdecls'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L155-L176'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootwalkdecls'>
			<code>
				walkDecls
				<span class='gray'>(prop?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each declaration node.</p>
<p>If you pass a filter, iteration will only happen over declarations
with matching properties.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter declarations by property name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkDecls(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  checkPropertySupport(decl.prop)
})

root.walkDecls(<span class="hljs-string">'border-radius'</span>, decl =&gt; {
  decl.remove()
})

root.walkDecls(<span class="hljs-regexp">/^background/</span>, decl =&gt; {
  decl.value = takeFirstColorFromGradient(decl.value)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rootwalkrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L201-L223'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rootwalkrules'>
			<code>
				walkRules
				<span class='gray'>(selector?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each rule node.</p>
<p>If you pass a filter, iteration will only happen over rules
with matching selectors.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>selector</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter rules by selector.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> selectors = []
root.walkRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  selectors.push(rule.selector)
})
<span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Your CSS uses <span class="hljs-subst">${ selectors.length }</span> selectors`</span>)</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='rule'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		<span class='font-smaller'>
	      Extends
	      
	        <a href="#container">Container</a>
	      
	    </span>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/rule.js#L15-L87'>
			<span>lib/rule.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rule'>
			<code>
				Rule
				<span class='gray'>(defaults)</span>
			</code>
		</a>
	</h3>

	<p>Represents a CSS rule: a selector followed by a declaration block.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				any
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a{}'</span>)
<span class="hljs-keyword">const</span> rule = root.first
rule.type       <span class="hljs-comment">//=&gt; 'rule'</span>
rule.toString() <span class="hljs-comment">//=&gt; 'a{}'</span></code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='ruleappend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L322-L331'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleappend'>
			<code>
				append
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the end of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.append(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleeach'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L63-L89'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleeach'>
			<code>
				each
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Iterates through the container’s immediate children,
calling <code>callback</code> for each child.</p>
<p>Returning <code>false</code> in the callback will break iteration.</p>
<p>This method only iterates through the container’s immediate children.
If you need to recursively iterate through all the container’s descendant
nodes, use <a href="#containerwalk">Container#walk</a>.</p>
<p>Unlike the for <code>{}</code>-cycle or <code>Array#forEach</code> this iterator is safe
if you are mutating the array of child nodes during iteration.
PostCSS will adjust the current index to match the mutations.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a { color: black; z-index: 1 }'</span>)
<span class="hljs-keyword">const</span> rule = root.first

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> decl <span class="hljs-keyword">of</span> rule.nodes) {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Cycle will be infinite, because cloneBefore moves the current node</span>
  <span class="hljs-comment">// to the next index</span>
}

rule.each(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  decl.cloneBefore({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop })
  <span class="hljs-comment">// Will be executed only for color and z-index</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleevery'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L539-L541'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleevery'>
			<code>
				every
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code>
for all of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is every child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> noPrefixes = rule.every(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] !== <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulefirst'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L582-L585'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulefirst'>
			<code>
				first
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s first child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.first === rules.nodes[<span class="hljs-number">0</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleindex'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L568-L572'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleindex'>
			<code>
				index
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Returns a <code>child</code>’s index within the <a href="Container#nodes">Container#nodes</a> array.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Child of the current container.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
	:
	<span class='force-inline'>Child index.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.index( rule.nodes[<span class="hljs-number">2</span>] ) <span class="hljs-comment">//=&gt; 2</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleinsertafter'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L414-L431'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleinsertafter'>
			<code>
				insertAfter
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node after old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='ruleinsertbefore'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L386-L404'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleinsertbefore'>
			<code>
				insertBefore
				<span class='gray'>(exist, add)</span>
			</code>
		</a>
	</h3>

	<p>Insert new node before old node within the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>exist</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>add</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.insertBefore(decl, decl.clone({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'-webkit-'</span> + decl.prop }))</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulelast'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L595-L598'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulelast'>
			<code>
				last
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The container’s last child.</p>

	
	<p>
		Type:
		<a href="#node">Node</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.last === rule.nodes[rule.nodes.length - <span class="hljs-number">1</span>]</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleprepend'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L353-L366'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleprepend'>
			<code>
				prepend
				<span class='gray'>(children)</span>
			</code>
		</a>
	</h3>

	<p>Inserts new nodes to the start of the container.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>children</code></td>
			<td class='col-3 quiet'>
				...(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>)
				
			</td>
			<td class='col-6'>New nodes.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> decl1 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })
<span class="hljs-keyword">const</span> decl2 = postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'background-color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'white'</span> })
rule.prepend(decl1, decl2)

root.append({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span>, <span class="hljs-attr">params</span>: <span class="hljs-string">'"UTF-8"'</span> })  <span class="hljs-comment">// at-rule</span>
root.append({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> })                       <span class="hljs-comment">// rule</span>
rule.append({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'black'</span> })       <span class="hljs-comment">// declaration</span>
rule.append({ <span class="hljs-attr">text</span>: <span class="hljs-string">'Comment'</span> })                     <span class="hljs-comment">// comment</span>

root.append(<span class="hljs-string">'a {}'</span>)
root.first.append(<span class="hljs-string">'color: black; z-index: 1'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleremoveall'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L475-L482'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleremoveall'>
			<code>
				removeAll
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Removes all children from the container
and cleans their parent properties.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.removeAll()
rule.nodes.length <span class="hljs-comment">//=&gt; 0</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleremovechild'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L447-L463'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleremovechild'>
			<code>
				removeChild
				<span class='gray'>(child)</span>
			</code>
		</a>
	</h3>

	<p>Removes node from the container and cleans the parent properties
from the node and its children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>child</code></td>
			<td class='col-3 quiet'>
				(<a href="#node">Node</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
				
			</td>
			<td class='col-6'>Child or child’s index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>rule.nodes.length  <span class="hljs-comment">//=&gt; 5</span>
rule.removeChild(decl)
rule.nodes.length  <span class="hljs-comment">//=&gt; 4</span>
decl.parent        <span class="hljs-comment">//=&gt; undefined</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulereplacevalues'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L510-L526'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulereplacevalues'>
			<code>
				replaceValues
				<span class='gray'>(pattern, opts, callback)</span>
			</code>
		</a>
	</h3>

	<p>Passes all declaration values within the container that match pattern
through callback, replacing those values with the returned result
of callback.</p>
<p>This method is useful if you are using a custom unit or function
and need to iterate through all values.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>pattern</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)
				
			</td>
			<td class='col-6'>Replace pattern.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-6'>Options to speed up the search.
</td>
		</tr>
		
		
		<tr>
  <td class='col-2 strong'>opts.props</td>
  <td class="col-2 quiet">
    (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)
    
  </td>
  <td class='col-8'>An array of property names.
</td>
</tr>


		
		<tr>
  <td class='col-2 strong'>opts.fast</td>
  <td class="col-2 quiet">
    <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    
  </td>
  <td class='col-8'>String that’s used to narrow down
values and speed up the regexp search.
</td>
</tr>


		
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
				
			</td>
			<td class='col-6'>String to replace pattern or callback
that returns a new value. The callback
will receive the same arguments
as those passed to a function parameter
of 
<code>String#replace</code>
.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#node">Node</a></code>
	:
	<span class='force-inline'>This node for methods chain.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.replaceValues(<span class="hljs-regexp">/\d+rem/</span>, { <span class="hljs-attr">fast</span>: <span class="hljs-string">'rem'</span> }, string =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-number">15</span> * <span class="hljs-built_in">parseInt</span>(string) + <span class="hljs-string">'px'</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='ruleselectors'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/rule.js#L38-L40'>
			<span>lib/rule.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#ruleselectors'>
			<code>
				selectors
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>An array containing the rule’s individual selectors.
Groups of selectors are split at commas.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> root = postcss.parse(<span class="hljs-string">'a, b { }'</span>)
<span class="hljs-keyword">const</span> rule = root.first

rule.selector  <span class="hljs-comment">//=&gt; 'a, b'</span>
rule.selectors <span class="hljs-comment">//=&gt; ['a', 'b']</span>

rule.selectors = [<span class="hljs-string">'a'</span>, <span class="hljs-string">'strong'</span>]
rule.selector <span class="hljs-comment">//=&gt; 'a, strong'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulesome'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L554-L556'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulesome'>
			<code>
				some
				<span class='gray'>(condition)</span>
			</code>
		</a>
	</h3>

	<p>Returns <code>true</code> if callback returns <code>true</code> for (at least) one
of the container’s children.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>condition</code></td>
			<td class='col-3 quiet'>
				<a href="#childcondition">childCondition</a>
				
			</td>
			<td class='col-6'>Iterator returns true or false.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>Is some child pass condition.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> hasPrefix = rule.some(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.prop[<span class="hljs-number">0</span>] === <span class="hljs-string">'-'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulewalk'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L110-L124'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulewalk'>
			<code>
				walk
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each node.</p>
<p>Like container.each(), this method is safe to use
if you are mutating arrays during iteration.</p>
<p>If you only need to iterate through the container’s immediate children,
use <a href="#containereach">Container#each</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walk(<span class="hljs-function"><span class="hljs-params">node</span> =&gt;</span> {
  <span class="hljs-comment">// Traverses all descendant nodes.</span>
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulewalkatrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L255-L276'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulewalkatrules'>
			<code>
				walkAtRules
				<span class='gray'>(name?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each at-rule node.</p>
<p>If you pass a filter, iteration will only happen over at-rules
that have matching names.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>name</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter at-rules by name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkAtRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  <span class="hljs-keyword">if</span> (isOld(rule.name)) rule.remove()
})

<span class="hljs-keyword">let</span> first = <span class="hljs-literal">false</span>
root.walkAtRules(<span class="hljs-string">'charset'</span>, rule =&gt; {
  <span class="hljs-keyword">if</span> (!first) {
    first = <span class="hljs-literal">true</span>
  } <span class="hljs-keyword">else</span> {
    rule.remove()
  }
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulewalkcomments'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L294-L300'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulewalkcomments'>
			<code>
				walkComments
				<span class='gray'>(callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each comment node.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkComments(<span class="hljs-function"><span class="hljs-params">comment</span> =&gt;</span> {
  comment.remove()
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulewalkdecls'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L155-L176'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulewalkdecls'>
			<code>
				walkDecls
				<span class='gray'>(prop?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each declaration node.</p>
<p>If you pass a filter, iteration will only happen over declarations
with matching properties.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter declarations by property name.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>root.walkDecls(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
  checkPropertySupport(decl.prop)
})

root.walkDecls(<span class="hljs-string">'border-radius'</span>, decl =&gt; {
  decl.remove()
})

root.walkDecls(<span class="hljs-regexp">/^background/</span>, decl =&gt; {
  decl.value = takeFirstColorFromGradient(decl.value)
})</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='rulewalkrules'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L201-L223'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#rulewalkrules'>
			<code>
				walkRules
				<span class='gray'>(selector?, callback)</span>
			</code>
		</a>
	</h3>

	<p>Traverses the container’s descendant nodes, calling callback
for each rule node.</p>
<p>If you pass a filter, iteration will only happen over rules
with matching selectors.</p>
<p>Like <a href="#containereach">Container#each</a>, this method is safe
to use if you are mutating arrays during iteration.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>selector</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/RegExp">RegExp</a>)?
				
			</td>
			<td class='col-6'>String or regular expression
to filter rules by selector.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>callback</code></td>
			<td class='col-3 quiet'>
				<a href="#childiterator">childIterator</a>
				
			</td>
			<td class='col-6'>Iterator receives each node and index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>returns 
<code>false</code>
 if iteration was broke.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> selectors = []
root.walkRules(<span class="hljs-function"><span class="hljs-params">rule</span> =&gt;</span> {
  selectors.push(rule.selector)
})
<span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Your CSS uses <span class="hljs-subst">${ selectors.length }</span> selectors`</span>)</code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<section id='warning'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L9-L107'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warning'>
			<code>
				Warning
				<span class='gray'>(text, opts = {})</span>
			</code>
		</a>
	</h3>

	<p>Represents a plugin’s warning. It can be created using <a href="#nodewarn">Node#warn</a>.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>text</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Warning message.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>?
				
				= <code>{}</code>
			</td>
			<td class='col-6'>Warning options.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">if</span> (decl.important) {
  decl.warn(result, <span class="hljs-string">'Avoid !important'</span>, { <span class="hljs-attr">word</span>: <span class="hljs-string">'!important'</span> })
}</code></pre>
	
	

	

	
	<h4 class='caps quiet mb2 mt3'>Instance Members</h4>
	<div class="section-indent">
  
    <section id='warningtype'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L31-L31'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warningtype'>
			<code>
				type
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Type to filter warnings from <a href="#resultmessages">Result#messages</a>.
Always equal to <code>"warning"</code>.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> nonWarning = result.messages.filter(<span class="hljs-function"><span class="hljs-params">i</span> =&gt;</span> i.type !== <span class="hljs-string">'warning'</span>)</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='warningtext'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L40-L40'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warningtext'>
			<code>
				text
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>The warning message.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>warning.text <span class="hljs-comment">//=&gt; 'Try to avoid !important'</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='warningline'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L51-L51'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warningline'>
			<code>
				line
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Line in the input file with this warning’s source.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>warning.line <span class="hljs-comment">//=&gt; 5</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='warningcolumn'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L60-L60'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warningcolumn'>
			<code>
				column
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Column in the input file with this warning’s source.</p>

	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>warning.column <span class="hljs-comment">//=&gt; 6</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='warningtostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/warning.js#L74-L88'>
			<span>lib/warning.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#warningtostring'>
			<code>
				toString
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Returns a warning position and message.</p>

	

	
	
	
	
	
	

	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>Warning position and message.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>warning.toString() <span class="hljs-comment">//=&gt; 'postcss-lint:a.css:10:14: Avoid !important'</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	
</section>

				
				
				
				<div class="hide">
  <section class='py2 clearfix'>

    <h2 id='namespaces' class='mt0'>
      NAMESPACES
    </h2>

    
      

    
  </section>
</div>
				
				
				
				<section id='list'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/list.js#L10-L85'>
			<span>lib/list.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#list'>
			<code>
				list
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Contains helpers for safely splitting lists of CSS values,
preserving parentheses and quotes.</p>

	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> list = postcss.list</code></pre>
	
	

	
	<h4 class='caps quiet mb2 mt3'>Static Members</h4>
	<div class="section-indent">
  
    <section id='listspace'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/list.js#L64-L67'>
			<span>lib/list.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#listspace'>
			<code>
				space
				<span class='gray'>(string)</span>
			</code>
		</a>
	</h3>

	<p>Safely splits space-separated values (such as those for <code>background</code>,
<code>border-radius</code>, and other shorthand properties).</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>string</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Space-separated values.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></code>
	:
	<span class='force-inline'>Split values.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.list.space(<span class="hljs-string">'1px calc(10% + 1px)'</span>) <span class="hljs-comment">//=&gt; ['1px', 'calc(10% + 1px)']</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='listcomma'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/list.js#L81-L83'>
			<span>lib/list.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#listcomma'>
			<code>
				comma
				<span class='gray'>(string)</span>
			</code>
		</a>
	</h3>

	<p>Safely splits comma-separated values (such as those for <code>transition-*</code>
and <code>background</code> properties).</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>string</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Comma-separated values.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></code>
	:
	<span class='force-inline'>Split values.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.list.comma(<span class="hljs-string">'black, linear-gradient(white, black)'</span>)
<span class="hljs-comment">//=&gt; ['black', 'linear-gradient(white, black)']</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	

	
</section>

				
				
				
				<section id='postcss'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L30-L35'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcss'>
			<code>
				postcss
				<span class='gray'>(plugins)</span>
			</code>
		</a>
	</h3>

	<p>Create a new <a href="#processor">Processor</a> instance that will apply <code>plugins</code>
as CSS processors.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>plugins</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="#plugin">Plugin</a> | <a href="#pluginfunction">pluginFunction</a>)> | <a href="#processor">Processor</a>)
				
			</td>
			<td class='col-6'>PostCSS plugins.
See 
<a href="#processoruse">Processor#use</a>
 for plugin format.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#processor">Processor</a></code>
	:
	<span class='force-inline'>Processor to process multiple CSS.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">let</span> postcss = <span class="hljs-built_in">require</span>(<span class="hljs-string">'postcss'</span>)

postcss(plugins).process(css, { <span class="hljs-keyword">from</span>, to }).then(<span class="hljs-function"><span class="hljs-params">result</span> =&gt;</span> {
  <span class="hljs-built_in">console</span>.log(result.css)
})</code></pre>
	
	

	
	<h4 class='caps quiet mb2 mt3'>Static Members</h4>
	<div class="section-indent">
  
    <section id='postcssplugin'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L109-L130'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssplugin'>
			<code>
				plugin
				<span class='gray'>(name, initializer)</span>
			</code>
		</a>
	</h3>

	<p>Creates a PostCSS plugin with a standard API.</p>
<p>The newly-wrapped function will provide both the name and PostCSS
version of the plugin.</p>
<pre class='hljs'><span class="hljs-keyword">const</span> processor = postcss([replace])
processor.plugins[<span class="hljs-number">0</span>].postcssPlugin  <span class="hljs-comment">//=&gt; 'postcss-replace'</span>
processor.plugins[<span class="hljs-number">0</span>].postcssVersion <span class="hljs-comment">//=&gt; '6.0.0'</span></pre>
<p>The plugin function receives 2 arguments: <a href="#root">Root</a>
and <a href="#result">Result</a> instance. The function should mutate the provided
<code>Root</code> node. Alternatively, you can create a new <code>Root</code> node
and override the <code>result.root</code> property.</p>
<pre class='hljs'><span class="hljs-keyword">const</span> cleaner = postcss.plugin(<span class="hljs-string">'postcss-cleaner'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    result.root = postcss.root()
  }
})</pre>
<p>As a convenience, plugins also expose a <code>process</code> method so that you can use
them as standalone tools.</p>
<pre class='hljs'>cleaner.process(css, processOpts, pluginOpts)
<span class="hljs-comment">// This is equivalent to:</span>
postcss([ cleaner(pluginOpts) ]).process(css, processOpts)</pre>
<p>Asynchronous plugins should return a <code>Promise</code> instance.</p>
<pre class='hljs'>postcss.plugin(<span class="hljs-string">'postcss-import'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    <span class="hljs-keyword">return</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Promise</span>( <span class="hljs-function">(<span class="hljs-params">resolve, reject</span>) =&gt;</span> {
      fs.readFile(<span class="hljs-string">'base.css'</span>, (base) =&gt; {
        root.prepend(base)
        resolve()
      })
    })
  }
})</pre>
<p>Add warnings using the <a href="#nodewarn">Node#warn</a> method.
Send data to other plugins using the <a href="#resultmessages">Result#messages</a> array.</p>
<pre class='hljs'>postcss.plugin(<span class="hljs-string">'postcss-caniuse-test'</span>, () =&gt; {
  <span class="hljs-keyword">return</span> <span class="hljs-function">(<span class="hljs-params">root, result</span>) =&gt;</span> {
    root.walkDecls(<span class="hljs-function"><span class="hljs-params">decl</span> =&gt;</span> {
      <span class="hljs-keyword">if</span> (!caniuse.support(decl.prop)) {
        decl.warn(result, <span class="hljs-string">'Some browsers do not support '</span> + decl.prop)
      }
    })
  }
})</pre>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>name</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>PostCSS plugin name. Same as in 
<code>name</code>

property in 
<code>package.json</code>
. It will be saved
in 
<code>plugin.postcssPlugin</code>
 property.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>initializer</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a>
				
			</td>
			<td class='col-6'>Will receive plugin options
and should return 
<a href="#pluginfunction">pluginFunction</a>
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#plugin">Plugin</a></code>
	:
	<span class='force-inline'>PostCSS plugin.
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='postcssstringify'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L143-L143'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssstringify'>
			<code>
				stringify
				<span class='gray'>(node, builder)</span>
			</code>
		</a>
	</h3>

	<p>Default function to convert a node tree into a CSS string.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Start node for stringifing. Usually 
<a href="#root">Root</a>
.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>builder</code></td>
			<td class='col-3 quiet'>
				<a href="#builder">builder</a>
				
			</td>
			<td class='col-6'>Function to concatenate CSS from node’s parts
or generate string and source map.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>void</code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	

	

	

	

	
</section>

  
    <section id='postcssparse'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L163-L163'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssparse'>
			<code>
				parse
				<span class='gray'>(css, opts?)</span>
			</code>
		</a>
	</h3>

	<p>Parses source css and returns a new <a href="#root">Root</a> node,
which contains the source CSS nodes.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="#tostring">toString</a>)
				
			</td>
			<td class='col-6'>String with input CSS or any object
with toString() method, like a Buffer
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>?
				
			</td>
			<td class='col-6'>Options with only 
<code>from</code>
 and 
<code>map</code>
 keys.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>PostCSS AST.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-comment">// Simple CSS concatenation with source map support</span>
<span class="hljs-keyword">const</span> root1 = postcss.parse(css1, { <span class="hljs-attr">from</span>: file1 })
<span class="hljs-keyword">const</span> root2 = postcss.parse(css2, { <span class="hljs-attr">from</span>: file2 })
root1.append(root2).toResult().css</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcssvendor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L173-L173'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssvendor'>
			<code>
				vendor
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Contains the <a href="#vendor">vendor</a> module.</p>

	
	<p>
		Type:
		<a href="#vendor">vendor</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.vendor.unprefixed(<span class="hljs-string">'-moz-tab'</span>) <span class="hljs-comment">//=&gt; ['tab']</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcsslist'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L183-L183'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcsslist'>
			<code>
				list
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Contains the <a href="#list">list</a> module.</p>

	
	<p>
		Type:
		<a href="#list">list</a>
	</p>
	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.list.space(<span class="hljs-string">'5px calc(10% + 5px)'</span>) <span class="hljs-comment">//=&gt; ['5px', 'calc(10% + 5px)']</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcsscomment'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L195-L195'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcsscomment'>
			<code>
				comment
				<span class='gray'>(defaults?)</span>
			</code>
		</a>
	</h3>

	<p>Creates a new <a href="#comment">Comment</a> node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Properties for the new node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#comment">Comment</a></code>
	:
	<span class='force-inline'>New comment node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.comment({ <span class="hljs-attr">text</span>: <span class="hljs-string">'test'</span> })</code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcssatrule'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L207-L207'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssatrule'>
			<code>
				atRule
				<span class='gray'>(defaults?)</span>
			</code>
		</a>
	</h3>

	<p>Creates a new <a href="#atrule">AtRule</a> node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Properties for the new node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#atrule">AtRule</a></code>
	:
	<span class='force-inline'>new at-rule node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.atRule({ <span class="hljs-attr">name</span>: <span class="hljs-string">'charset'</span> }).toString() <span class="hljs-comment">//=&gt; "@charset"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcssdecl'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L219-L219'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssdecl'>
			<code>
				decl
				<span class='gray'>(defaults?)</span>
			</code>
		</a>
	</h3>

	<p>Creates a new <a href="#declaration">Declaration</a> node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Properties for the new node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#declaration">Declaration</a></code>
	:
	<span class='force-inline'>new declaration node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.decl({ <span class="hljs-attr">prop</span>: <span class="hljs-string">'color'</span>, <span class="hljs-attr">value</span>: <span class="hljs-string">'red'</span> }).toString() <span class="hljs-comment">//=&gt; "color: red"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcssrule'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L231-L231'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssrule'>
			<code>
				rule
				<span class='gray'>(defaults?)</span>
			</code>
		</a>
	</h3>

	<p>Creates a new <a href="#rule">Rule</a> node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Properties for the new node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#rule">Rule</a></code>
	:
	<span class='force-inline'>new rule node
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.rule({ <span class="hljs-attr">selector</span>: <span class="hljs-string">'a'</span> }).toString() <span class="hljs-comment">//=&gt; "a {\n}"</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='postcssroot'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/postcss.js#L243-L243'>
			<span>lib/postcss.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#postcssroot'>
			<code>
				root
				<span class='gray'>(defaults?)</span>
			</code>
		</a>
	</h3>

	<p>Creates a new <a href="#root">Root</a> node.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>defaults</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?
				
			</td>
			<td class='col-6'>Properties for the new node.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>new root node.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.root({ <span class="hljs-attr">after</span>: <span class="hljs-string">'\n'</span> }).toString() <span class="hljs-comment">//=&gt; "\n"</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	

	
</section>

				
				
				
				<section id='vendor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/vendor.js#L9-L45'>
			<span>lib/vendor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#vendor'>
			<code>
				vendor
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	<p>Contains helpers for working with vendor prefixes.</p>

	

	
	
	
	
	
	

	

	

	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code><span class="hljs-keyword">const</span> vendor = postcss.vendor</code></pre>
	
	

	
	<h4 class='caps quiet mb2 mt3'>Static Members</h4>
	<div class="section-indent">
  
    <section id='vendorprefix'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/vendor.js#L22-L29'>
			<span>lib/vendor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#vendorprefix'>
			<code>
				prefix
				<span class='gray'>(prop)</span>
			</code>
		</a>
	</h3>

	<p>Returns the vendor prefix extracted from an input string.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>String with or without vendor prefix.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>vendor prefix or empty string
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.vendor.prefix(<span class="hljs-string">'-moz-tab-size'</span>) <span class="hljs-comment">//=&gt; '-moz-'</span>
postcss.vendor.prefix(<span class="hljs-string">'tab-size'</span>)      <span class="hljs-comment">//=&gt; ''</span></code></pre>
	
	

	

	

	

	
</section>

  
    <section id='vendorunprefixed'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/vendor.js#L41-L43'>
			<span>lib/vendor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#vendorunprefixed'>
			<code>
				unprefixed
				<span class='gray'>(prop)</span>
			</code>
		</a>
	</h3>

	<p>Returns the input string stripped of its vendor prefix.</p>

	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>prop</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>String with or without vendor prefix.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
	:
	<span class='force-inline'>String name without vendor prefixes.
</span>
	
	
	

	

	
	<h4 class='caps quiet mb1 mt3'>Examples</h4>
	
	
	<pre class='p1 overflow-auto round fill-light'><code>postcss.vendor.unprefixed(<span class="hljs-string">'-moz-tab-size'</span>) <span class="hljs-comment">//=&gt; 'tab-size'</span></code></pre>
	
	

	

	

	

	
</section>

  
</div>

	

	

	

	
</section>

				
				
				
				<div class="hide">
  <section class='py2 clearfix'>

    <h2 id='global' class='mt0'>
      GLOBAL
    </h2>

    
      

    
  </section>
</div>
				
				
				
				<section id='message'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/result.js#L174-L178'>
			<span>lib/result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#message'>
			<code>
				Message
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>type</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-8'>: Message type.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>plugin</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-8'>: Source PostCSS plugin name.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='plugin'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L183-L186'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#plugin'>
			<code>
				Plugin
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>postcss</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a>
				
			</td>
			<td class='col-8'>: PostCSS plugin function.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='builder'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L139-L144'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#builder'>
			<code>
				builder
				<span class='gray'>(part, node, type?)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>part</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-6'>Part of generated CSS connected to this node.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>AST node.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>type</code></td>
			<td class='col-3 quiet'>
				(<code>"start"</code> | <code>"end"</code>)?
				
			</td>
			<td class='col-6'>Node’s part type.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='childcondition'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L697-L703'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#childcondition'>
			<code>
				childCondition
				<span class='gray'>(node, index, nodes)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Container child.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>index</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-6'>Child index.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>nodes</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#node">Node</a>>
				
			</td>
			<td class='col-6'>All container children.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	

	

	

	

	
</section>

				
				
				
				<section id='childiterator'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/container.js#L705-L710'>
			<span>lib/container.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#childiterator'>
			<code>
				childIterator
				<span class='gray'>(node, index)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Container child.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>index</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-6'>Child index.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>(<code>false</code> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/undefined">undefined</a>)</code>
	:
	<span class='force-inline'>Returning 
<code>false</code>
 will break iteration.
</span>
	
	
	

	

	

	

	

	

	
</section>

				
				
				
				<section id='fileposition'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/input.js#L171-L176'>
			<span>lib/input.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#fileposition'>
			<code>
				filePosition
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>file</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-8'>: Path to file.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>line</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-8'>: Source line in file.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>column</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-8'>: Source column in file.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='onfulfilled'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L431-L434'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#onfulfilled'>
			<code>
				onFulfilled
				<span class='gray'>(result)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='onrejected'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/lazy-result.js#L436-L439'>
			<span>lib/lazy-result.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#onrejected'>
			<code>
				onRejected
				<span class='gray'>(error)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>error</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Error">Error</a>
				
			</td>
			<td class='col-6'></td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='parser'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L146-L154'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#parser'>
			<code>
				parser
				<span class='gray'>(css, opts?)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>css</code></td>
			<td class='col-3 quiet'>
				(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="#tostring">toString</a>)
				
			</td>
			<td class='col-6'>String with input CSS or any object
with toString() method, like a Buffer.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>opts</code></td>
			<td class='col-3 quiet'>
				<a href="#processoptions">processOptions</a>?
				
			</td>
			<td class='col-6'>Options with only 
<code>from</code>
 and 
<code>map</code>
 keys.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code><a href="#root">Root</a></code>
	:
	<span class='force-inline'>PostCSS AST
</span>
	
	
	

	

	

	

	

	

	
</section>

				
				
				
				<section id='pluginfunction'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L177-L181'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#pluginfunction'>
			<code>
				pluginFunction
				<span class='gray'>(root, result)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>root</code></td>
			<td class='col-3 quiet'>
				<a href="#root">Root</a>
				
			</td>
			<td class='col-6'>Parsed input CSS.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>result</code></td>
			<td class='col-3 quiet'>
				<a href="#result">Result</a>
				
			</td>
			<td class='col-6'>Result to set warnings or check other plugins.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='position'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L568-L572'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#position'>
			<code>
				position
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>line</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-8'>: Source line in file.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>column</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-8'>: Source column in file.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='processoptions'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L188-L219'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#processoptions'>
			<code>
				processOptions
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>from</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-8'>: The path of the CSS source file.
You should always set 
<code>from</code>
,
because it is used in source map
generation and syntax error messages.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>to</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
				
			</td>
			<td class='col-8'>: The path where you’ll put the output
CSS file. You should always set 
<code>to</code>

to generate correct source maps.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>parser</td>
			<td class='col-2 quiet'>
				<a href="#parser">parser</a>
				
			</td>
			<td class='col-8'>: Function to generate AST by string.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>stringifier</td>
			<td class='col-2 quiet'>
				<a href="#stringifier">stringifier</a>
				
			</td>
			<td class='col-8'>: Class to generate string by AST.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>syntax</td>
			<td class='col-2 quiet'>
				<a href="#syntax">syntax</a>
				
			</td>
			<td class='col-8'>: Object with 
<code>parse</code>
 and 
<code>stringify</code>
.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>map</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-8'>: Source map options.
</td>
		</tr>

		
		<tr>
			<td class='col-2 strong'>map</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
				
			</td>
			<td class='col-8'>: Source map options.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='source'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/node.js#L574-L579'>
			<span>lib/node.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#source'>
			<code>
				source
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>input</td>
			<td class='col-2 quiet'>
				<a href="#input">Input</a>
				
			</td>
			<td class='col-8'>: <a href="#input">Input</a>
 with input file
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>start</td>
			<td class='col-2 quiet'>
				<a href="#position">position</a>
				
			</td>
			<td class='col-8'>: The starting position of the node’s source.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>end</td>
			<td class='col-2 quiet'>
				<a href="#position">position</a>
				
			</td>
			<td class='col-8'>: The ending position of the node’s source.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='stringifier'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L156-L164'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#stringifier'>
			<code>
				stringifier
				<span class='gray'>(node, builder)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Start node for stringifing. Usually 
<a href="#root">Root</a>
.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>builder</code></td>
			<td class='col-3 quiet'>
				<a href="#builder">builder</a>
				
			</td>
			<td class='col-6'>Function to concatenate CSS from node’s parts
or generate string and source map.
</td>
		</tr>
		
		
	</table>
	

	

	
	
	<h4 class='caps quiet mb1 mt3'>Returns</h4>
	<code>void</code>
	:
	<span class='force-inline'>
</span>
	
	
	

	

	

	

	

	

	
</section>

				
				
				
				<section id='syntax'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L166-L170'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#syntax'>
			<code>
				syntax
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>parse</td>
			<td class='col-2 quiet'>
				<a href="#parser">parser</a>
				
			</td>
			<td class='col-8'>: Function to generate AST by string.
</td>
		</tr>

		
		
		<tr>
			<td class='col-2 strong'>stringify</td>
			<td class='col-2 quiet'>
				<a href="#stringifier">stringifier</a>
				
			</td>
			<td class='col-8'>: Function to generate string by AST.
</td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='tostring'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/processor.js#L172-L175'>
			<span>lib/processor.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#tostring'>
			<code>
				toString
				<span class='gray'>()</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
	</p>
	

	
	
	
	
	
	

	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-2 small caps quiet'>property</th>
		<th class='col-2 small caps quiet'>type</th>
		<th class='col-8 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-2 strong'>toString</td>
			<td class='col-2 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">function</a>
				
			</td>
			<td class='col-8'></td>
		</tr>

		
		
	</table>
	

	

	

	

	

	

	

	
</section>

				
				
				
				<section id='visitor'class='mt2 mb2 px3 py1 keyline-top'>

	<div class='right py2'>
		
		
		<span class="px2"></span>
		<a class='fr fill-darken0 round round pad1x quiet h5' href='https://**************/:postcss/postcss/blob/ae7cf844d806d0e9f536cfd5b5a680070ebed3ce/lib/root.js#L118-L122'>
			<span>lib/root.js</span>
		</a>
		
	</div>

	<h3 class='regular'>
		<a class='black' href='#visitor'>
			<code>
				visitor
				<span class='gray'>(node, index)</span>
			</code>
		</a>
	</h3>

	
	
	<p>
		Type:
		<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function">Function</a>
	</p>
	

	
	
	
	
	
	

	
	<table class='table-light mt3 rounded keyline-all keyline-light overflow-hidden bg-cloudy-light'>
		<thead class='fill-light'>
		<th class='col-3 small caps quiet'>parameter</th>
		<th class='col-3 small caps quiet'>type</th>
		<th class='col-6 small caps quiet'>description</th>
		</thead>
		
		<tr>
			<td class='col-3 strong'><code>node</code></td>
			<td class='col-3 quiet'>
				<a href="#node">Node</a>
				
			</td>
			<td class='col-6'>Container child.
</td>
		</tr>
		
		
		<tr>
			<td class='col-3 strong'><code>index</code></td>
			<td class='col-3 quiet'>
				<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
				
			</td>
			<td class='col-6'>Child index.
</td>
		</tr>
		
		
	</table>
	

	

	

	

	

	

	

	

	
</section>

				
				
			</div>
		</div>
	</div>
</div>
<script src='assets/scripts.min.js'></script>
</body>
</html>
