"use strict";

exports.__esModule = true;
exports.default = void 0;

var _declaration = _interopRequireDefault(require("./declaration"));

var _tokenize = _interopRequireDefault(require("./tokenize"));

var _comment = _interopRequireDefault(require("./comment"));

var _atRule = _interopRequireDefault(require("./at-rule"));

var _root = _interopRequireDefault(require("./root"));

var _rule = _interopRequireDefault(require("./rule"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var Parser =
/*#__PURE__*/
function () {
  function Parser(input) {
    this.input = input;
    this.root = new _root.default();
    this.current = this.root;
    this.spaces = '';
    this.semicolon = false;
    this.createTokenizer();
    this.root.source = {
      input: input,
      start: {
        line: 1,
        column: 1
      }
    };
  }

  var _proto = Parser.prototype;

  _proto.createTokenizer = function createTokenizer() {
    this.tokenizer = (0, _tokenize.default)(this.input);
  };

  _proto.parse = function parse() {
    var token;

    while (!this.tokenizer.endOfFile()) {
      token = this.tokenizer.nextToken();

      switch (token[0]) {
        case 'space':
          this.spaces += token[1];
          break;

        case ';':
          this.freeSemicolon(token);
          break;

        case '}':
          this.end(token);
          break;

        case 'comment':
          this.comment(token);
          break;

        case 'at-word':
          this.atrule(token);
          break;

        case '{':
          this.emptyRule(token);
          break;

        default:
          this.other(token);
          break;
      }
    }

    this.endFile();
  };

  _proto.comment = function comment(token) {
    var node = new _comment.default();
    this.init(node, token[2], token[3]);
    node.source.end = {
      line: token[4],
      column: token[5]
    };
    var text = token[1].slice(2, -2);

    if (/^\s*$/.test(text)) {
      node.text = '';
      node.raws.left = text;
      node.raws.right = '';
    } else {
      var match = text.match(/^(\s*)([^]*[^\s])(\s*)$/);
      node.text = match[2];
      node.raws.left = match[1];
      node.raws.right = match[3];
    }
  };

  _proto.emptyRule = function emptyRule(token) {
    var node = new _rule.default();
    this.init(node, token[2], token[3]);
    node.selector = '';
    node.raws.between = '';
    this.current = node;
  };

  _proto.other = function other(start) {
    var end = false;
    var type = null;
    var colon = false;
    var bracket = null;
    var brackets = [];
    var tokens = [];
    var token = start;

    while (token) {
      type = token[0];
      tokens.push(token);

      if (type === '(' || type === '[') {
        if (!bracket) bracket = token;
        brackets.push(type === '(' ? ')' : ']');
      } else if (brackets.length === 0) {
        if (type === ';') {
          if (colon) {
            this.decl(tokens);
            return;
          } else {
            break;
          }
        } else if (type === '{') {
          this.rule(tokens);
          return;
        } else if (type === '}') {
          this.tokenizer.back(tokens.pop());
          end = true;
          break;
        } else if (type === ':') {
          colon = true;
        }
      } else if (type === brackets[brackets.length - 1]) {
        brackets.pop();
        if (brackets.length === 0) bracket = null;
      }

      token = this.tokenizer.nextToken();
    }

    if (this.tokenizer.endOfFile()) end = true;
    if (brackets.length > 0) this.unclosedBracket(bracket);

    if (end && colon) {
      while (tokens.length) {
        token = tokens[tokens.length - 1][0];
        if (token !== 'space' && token !== 'comment') break;
        this.tokenizer.back(tokens.pop());
      }

      this.decl(tokens);
    } else {
      this.unknownWord(tokens);
    }
  };

  _proto.rule = function rule(tokens) {
    tokens.pop();
    var node = new _rule.default();
    this.init(node, tokens[0][2], tokens[0][3]);
    node.raws.between = this.spacesAndCommentsFromEnd(tokens);
    this.raw(node, 'selector', tokens);
    this.current = node;
  };

  _proto.decl = function decl(tokens) {
    var node = new _declaration.default();
    this.init(node);
    var last = tokens[tokens.length - 1];

    if (last[0] === ';') {
      this.semicolon = true;
      tokens.pop();
    }

    if (last[4]) {
      node.source.end = {
        line: last[4],
        column: last[5]
      };
    } else {
      node.source.end = {
        line: last[2],
        column: last[3]
      };
    }

    while (tokens[0][0] !== 'word') {
      if (tokens.length === 1) this.unknownWord(tokens);
      node.raws.before += tokens.shift()[1];
    }

    node.source.start = {
      line: tokens[0][2],
      column: tokens[0][3]
    };
    node.prop = '';

    while (tokens.length) {
      var type = tokens[0][0];

      if (type === ':' || type === 'space' || type === 'comment') {
        break;
      }

      node.prop += tokens.shift()[1];
    }

    node.raws.between = '';
    var token;

    while (tokens.length) {
      token = tokens.shift();

      if (token[0] === ':') {
        node.raws.between += token[1];
        break;
      } else {
        if (token[0] === 'word' && /\w/.test(token[1])) {
          this.unknownWord([token]);
        }

        node.raws.between += token[1];
      }
    }

    if (node.prop[0] === '_' || node.prop[0] === '*') {
      node.raws.before += node.prop[0];
      node.prop = node.prop.slice(1);
    }

    node.raws.between += this.spacesAndCommentsFromStart(tokens);
    this.precheckMissedSemicolon(tokens);

    for (var i = tokens.length - 1; i > 0; i--) {
      token = tokens[i];

      if (token[1].toLowerCase() === '!important') {
        node.important = true;
        var string = this.stringFrom(tokens, i);
        string = this.spacesFromEnd(tokens) + string;
        if (string !== ' !important') node.raws.important = string;
        break;
      } else if (token[1].toLowerCase() === 'important') {
        var cache = tokens.slice(0);
        var str = '';

        for (var j = i; j > 0; j--) {
          var _type = cache[j][0];

          if (str.trim().indexOf('!') === 0 && _type !== 'space') {
            break;
          }

          str = cache.pop()[1] + str;
        }

        if (str.trim().indexOf('!') === 0) {
          node.important = true;
          node.raws.important = str;
          tokens = cache;
        }
      }

      if (token[0] !== 'space' && token[0] !== 'comment') {
        break;
      }
    }

    this.raw(node, 'value', tokens);
    if (node.value.indexOf(':') !== -1) this.checkMissedSemicolon(tokens);
  };

  _proto.atrule = function atrule(token) {
    var node = new _atRule.default();
    node.name = token[1].slice(1);

    if (node.name === '') {
      this.unnamedAtrule(node, token);
    }

    this.init(node, token[2], token[3]);
    var prev;
    var shift;
    var last = false;
    var open = false;
    var params = [];

    while (!this.tokenizer.endOfFile()) {
      token = this.tokenizer.nextToken();

      if (token[0] === ';') {
        node.source.end = {
          line: token[2],
          column: token[3]
        };
        this.semicolon = true;
        break;
      } else if (token[0] === '{') {
        open = true;
        break;
      } else if (token[0] === '}') {
        if (params.length > 0) {
          shift = params.length - 1;
          prev = params[shift];

          while (prev && prev[0] === 'space') {
            prev = params[--shift];
          }

          if (prev) {
            node.source.end = {
              line: prev[4],
              column: prev[5]
            };
          }
        }

        this.end(token);
        break;
      } else {
        params.push(token);
      }

      if (this.tokenizer.endOfFile()) {
        last = true;
        break;
      }
    }

    node.raws.between = this.spacesAndCommentsFromEnd(params);

    if (params.length) {
      node.raws.afterName = this.spacesAndCommentsFromStart(params);
      this.raw(node, 'params', params);

      if (last) {
        token = params[params.length - 1];
        node.source.end = {
          line: token[4],
          column: token[5]
        };
        this.spaces = node.raws.between;
        node.raws.between = '';
      }
    } else {
      node.raws.afterName = '';
      node.params = '';
    }

    if (open) {
      node.nodes = [];
      this.current = node;
    }
  };

  _proto.end = function end(token) {
    if (this.current.nodes && this.current.nodes.length) {
      this.current.raws.semicolon = this.semicolon;
    }

    this.semicolon = false;
    this.current.raws.after = (this.current.raws.after || '') + this.spaces;
    this.spaces = '';

    if (this.current.parent) {
      this.current.source.end = {
        line: token[2],
        column: token[3]
      };
      this.current = this.current.parent;
    } else {
      this.unexpectedClose(token);
    }
  };

  _proto.endFile = function endFile() {
    if (this.current.parent) this.unclosedBlock();

    if (this.current.nodes && this.current.nodes.length) {
      this.current.raws.semicolon = this.semicolon;
    }

    this.current.raws.after = (this.current.raws.after || '') + this.spaces;
  };

  _proto.freeSemicolon = function freeSemicolon(token) {
    this.spaces += token[1];

    if (this.current.nodes) {
      var prev = this.current.nodes[this.current.nodes.length - 1];

      if (prev && prev.type === 'rule' && !prev.raws.ownSemicolon) {
        prev.raws.ownSemicolon = this.spaces;
        this.spaces = '';
      }
    }
  } // Helpers
  ;

  _proto.init = function init(node, line, column) {
    this.current.push(node);
    node.source = {
      start: {
        line: line,
        column: column
      },
      input: this.input
    };
    node.raws.before = this.spaces;
    this.spaces = '';
    if (node.type !== 'comment') this.semicolon = false;
  };

  _proto.raw = function raw(node, prop, tokens) {
    var token, type;
    var length = tokens.length;
    var value = '';
    var clean = true;
    var next, prev;
    var pattern = /^([.|#])?([\w])+/i;

    for (var i = 0; i < length; i += 1) {
      token = tokens[i];
      type = token[0];

      if (type === 'comment' && node.type === 'rule') {
        prev = tokens[i - 1];
        next = tokens[i + 1];

        if (prev[0] !== 'space' && next[0] !== 'space' && pattern.test(prev[1]) && pattern.test(next[1])) {
          value += token[1];
        } else {
          clean = false;
        }

        continue;
      }

      if (type === 'comment' || type === 'space' && i === length - 1) {
        clean = false;
      } else {
        value += token[1];
      }
    }

    if (!clean) {
      var raw = tokens.reduce(function (all, i) {
        return all + i[1];
      }, '');
      node.raws[prop] = {
        value: value,
        raw: raw
      };
    }

    node[prop] = value;
  };

  _proto.spacesAndCommentsFromEnd = function spacesAndCommentsFromEnd(tokens) {
    var lastTokenType;
    var spaces = '';

    while (tokens.length) {
      lastTokenType = tokens[tokens.length - 1][0];
      if (lastTokenType !== 'space' && lastTokenType !== 'comment') break;
      spaces = tokens.pop()[1] + spaces;
    }

    return spaces;
  };

  _proto.spacesAndCommentsFromStart = function spacesAndCommentsFromStart(tokens) {
    var next;
    var spaces = '';

    while (tokens.length) {
      next = tokens[0][0];
      if (next !== 'space' && next !== 'comment') break;
      spaces += tokens.shift()[1];
    }

    return spaces;
  };

  _proto.spacesFromEnd = function spacesFromEnd(tokens) {
    var lastTokenType;
    var spaces = '';

    while (tokens.length) {
      lastTokenType = tokens[tokens.length - 1][0];
      if (lastTokenType !== 'space') break;
      spaces = tokens.pop()[1] + spaces;
    }

    return spaces;
  };

  _proto.stringFrom = function stringFrom(tokens, from) {
    var result = '';

    for (var i = from; i < tokens.length; i++) {
      result += tokens[i][1];
    }

    tokens.splice(from, tokens.length - from);
    return result;
  };

  _proto.colon = function colon(tokens) {
    var brackets = 0;
    var token, type, prev;

    for (var i = 0; i < tokens.length; i++) {
      token = tokens[i];
      type = token[0];

      if (type === '(') {
        brackets += 1;
      }

      if (type === ')') {
        brackets -= 1;
      }

      if (brackets === 0 && type === ':') {
        if (!prev) {
          this.doubleColon(token);
        } else if (prev[0] === 'word' && prev[1] === 'progid') {
          continue;
        } else {
          return i;
        }
      }

      prev = token;
    }

    return false;
  } // Errors
  ;

  _proto.unclosedBracket = function unclosedBracket(bracket) {
    throw this.input.error('Unclosed bracket', bracket[2], bracket[3]);
  };

  _proto.unknownWord = function unknownWord(tokens) {
    throw this.input.error('Unknown word', tokens[0][2], tokens[0][3]);
  };

  _proto.unexpectedClose = function unexpectedClose(token) {
    throw this.input.error('Unexpected }', token[2], token[3]);
  };

  _proto.unclosedBlock = function unclosedBlock() {
    var pos = this.current.source.start;
    throw this.input.error('Unclosed block', pos.line, pos.column);
  };

  _proto.doubleColon = function doubleColon(token) {
    throw this.input.error('Double colon', token[2], token[3]);
  };

  _proto.unnamedAtrule = function unnamedAtrule(node, token) {
    throw this.input.error('At-rule without name', token[2], token[3]);
  };

  _proto.precheckMissedSemicolon = function precheckMissedSemicolon()
  /* tokens */
  {// Hook for Safe Parser
  };

  _proto.checkMissedSemicolon = function checkMissedSemicolon(tokens) {
    var colon = this.colon(tokens);
    if (colon === false) return;
    var founded = 0;
    var token;

    for (var j = colon - 1; j >= 0; j--) {
      token = tokens[j];

      if (token[0] !== 'space') {
        founded += 1;
        if (founded === 2) break;
      }
    }

    throw this.input.error('Missed semicolon', token[2], token[3]);
  };

  return Parser;
}();

exports.default = Parser;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf8;base64,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
