{"name": "resolve-url-loader", "version": "3.1.2", "description": "Webpack loader that resolves relative paths in url() statements based on the original source file", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/bholloway/resolve-url-loader.git"}, "keywords": ["webpack", "loader", "css", "normalize", "rewrite", "resolve", "url", "sass", "relative", "file"], "author": "bholloway", "license": "MIT", "bugs": {"url": "https://github.com/bholloway/resolve-url-loader/issues"}, "homepage": "https://github.com/bholloway/resolve-url-loader", "engines": {"node": ">=6.0.0"}, "files": ["index.js", "lib/**/+([a-z-]).js"], "scripts": {"lint": "jshint --exclude **/node_modules index.js lib"}, "dependencies": {"adjust-sourcemap-loader": "3.0.0", "camelcase": "5.3.1", "compose-function": "3.0.3", "convert-source-map": "1.7.0", "es6-iterator": "2.0.3", "loader-utils": "1.2.3", "postcss": "7.0.21", "rework": "1.0.1", "rework-visit": "1.0.0", "source-map": "0.6.1"}}