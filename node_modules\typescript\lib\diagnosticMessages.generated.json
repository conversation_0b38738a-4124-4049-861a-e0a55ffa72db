{"Unterminated_string_literal_1002": "Unterminated string literal.", "Identifier_expected_1003": "Identifier expected.", "_0_expected_1005": "'{0}' expected.", "A_file_cannot_have_a_reference_to_itself_1006": "A file cannot have a reference to itself.", "Trailing_comma_not_allowed_1009": "Trailing comma not allowed.", "Asterisk_Slash_expected_1010": "'*/' expected.", "An_element_access_expression_should_take_an_argument_1011": "An element access expression should take an argument.", "Unexpected_token_1012": "Unexpected token.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "A rest parameter or binding pattern may not have a trailing comma.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "A rest parameter must be last in a parameter list.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Parameter cannot have question mark and initializer.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "A required parameter cannot follow an optional parameter.", "An_index_signature_cannot_have_a_rest_parameter_1017": "An index signature cannot have a rest parameter.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "An index signature parameter cannot have an accessibility modifier.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "An index signature parameter cannot have a question mark.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "An index signature parameter cannot have an initializer.", "An_index_signature_must_have_a_type_annotation_1021": "An index signature must have a type annotation.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "An index signature parameter must have a type annotation.", "An_index_signature_parameter_type_must_be_string_or_number_1023": "An index signature parameter type must be 'string' or 'number'.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "'readonly' modifier can only appear on a property declaration or index signature.", "Accessibility_modifier_already_seen_1028": "Accessibility modifier already seen.", "_0_modifier_must_precede_1_modifier_1029": "'{0}' modifier must precede '{1}' modifier.", "_0_modifier_already_seen_1030": "'{0}' modifier already seen.", "_0_modifier_cannot_appear_on_a_class_element_1031": "'{0}' modifier cannot appear on a class element.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' must be followed by an argument list or member access.", "Only_ambient_modules_can_use_quoted_names_1035": "Only ambient modules can use quoted names.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Statements are not allowed in ambient contexts.", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "A 'declare' modifier cannot be used in an already ambient context.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Initializers are not allowed in ambient contexts.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "'{0}' modifier cannot be used in an ambient context.", "_0_modifier_cannot_be_used_with_a_class_declaration_1041": "'{0}' modifier cannot be used with a class declaration.", "_0_modifier_cannot_be_used_here_1042": "'{0}' modifier cannot be used here.", "_0_modifier_cannot_appear_on_a_data_property_1043": "'{0}' modifier cannot appear on a data property.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "'{0}' modifier cannot appear on a module or namespace element.", "A_0_modifier_cannot_be_used_with_an_interface_declaration_1045": "A '{0}' modifier cannot be used with an interface declaration.", "A_declare_modifier_is_required_for_a_top_level_declaration_in_a_d_ts_file_1046": "A 'declare' modifier is required for a top level declaration in a .d.ts file.", "A_rest_parameter_cannot_be_optional_1047": "A rest parameter cannot be optional.", "A_rest_parameter_cannot_have_an_initializer_1048": "A rest parameter cannot have an initializer.", "A_set_accessor_must_have_exactly_one_parameter_1049": "A 'set' accessor must have exactly one parameter.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "A 'set' accessor cannot have an optional parameter.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "A 'set' accessor parameter cannot have an initializer.", "A_set_accessor_cannot_have_rest_parameter_1053": "A 'set' accessor cannot have rest parameter.", "A_get_accessor_cannot_have_parameters_1054": "A 'get' accessor cannot have parameters.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "Type '{0}' is not a valid async function return type in ES5/ES3 because it does not refer to a Promise-compatible constructor value.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Accessors are only available when targeting ECMAScript 5 and higher.", "An_async_function_or_method_must_have_a_valid_awaitable_return_type_1057": "An async function or method must have a valid awaitable return type.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "The return type of an async function must either be a valid promise or must not contain a callable 'then' member.", "A_promise_must_have_a_then_method_1059": "A promise must have a 'then' method.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "The first parameter of the 'then' method of a promise must be a callback.", "Enum_member_must_have_initializer_1061": "Enum member must have initializer.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Type is referenced directly or indirectly in the fulfillment callback of its own 'then' method.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "An export assignment cannot be used in a namespace.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1064": "The return type of an async function or method must be the global Promise<T> type.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "In ambient enum declarations member initializer must be constant expression.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Unexpected token. A constructor, method, accessor, or property was expected.", "_0_modifier_cannot_appear_on_a_type_member_1070": "'{0}' modifier cannot appear on a type member.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "'{0}' modifier cannot appear on an index signature.", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "A '{0}' modifier cannot be used with an import declaration.", "Invalid_reference_directive_syntax_1084": "Invalid 'reference' directive syntax.", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "Octal literals are not available when targeting ECMAScript 5 and higher. Use the syntax '{0}'.", "An_accessor_cannot_be_declared_in_an_ambient_context_1086": "An accessor cannot be declared in an ambient context.", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "'{0}' modifier cannot appear on a constructor declaration.", "_0_modifier_cannot_appear_on_a_parameter_1090": "'{0}' modifier cannot appear on a parameter.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "Only a single variable declaration is allowed in a 'for...in' statement.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Type parameters cannot appear on a constructor declaration.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Type annotation cannot appear on a constructor declaration.", "An_accessor_cannot_have_type_parameters_1094": "An accessor cannot have type parameters.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "A 'set' accessor cannot have a return type annotation.", "An_index_signature_must_have_exactly_one_parameter_1096": "An index signature must have exactly one parameter.", "_0_list_cannot_be_empty_1097": "'{0}' list cannot be empty.", "Type_parameter_list_cannot_be_empty_1098": "Type parameter list cannot be empty.", "Type_argument_list_cannot_be_empty_1099": "Type argument list cannot be empty.", "Invalid_use_of_0_in_strict_mode_1100": "Invalid use of '{0}' in strict mode.", "with_statements_are_not_allowed_in_strict_mode_1101": "'with' statements are not allowed in strict mode.", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "'delete' cannot be called on an identifier in strict mode.", "A_for_await_of_statement_is_only_allowed_within_an_async_function_or_async_generator_1103": "A 'for-await-of' statement is only allowed within an async function or async generator.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "A 'continue' statement can only be used within an enclosing iteration statement.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "A 'break' statement can only be used within an enclosing iteration or switch statement.", "Jump_target_cannot_cross_function_boundary_1107": "Jump target cannot cross function boundary.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "A 'return' statement can only be used within a function body.", "Expression_expected_1109": "Expression expected.", "Type_expected_1110": "Type expected.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "A 'default' clause cannot appear more than once in a 'switch' statement.", "Duplicate_label_0_1114": "Duplicate label '{0}'.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "A 'continue' statement can only jump to a label of an enclosing iteration statement.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "A 'break' statement can only jump to a label of an enclosing statement.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_in_strict_mode_1117": "An object literal cannot have multiple properties with the same name in strict mode.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "An object literal cannot have multiple get/set accessors with the same name.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "An object literal cannot have property and accessor with the same name.", "An_export_assignment_cannot_have_modifiers_1120": "An export assignment cannot have modifiers.", "Octal_literals_are_not_allowed_in_strict_mode_1121": "Octal literals are not allowed in strict mode.", "A_tuple_type_element_list_cannot_be_empty_1122": "A tuple type element list cannot be empty.", "Variable_declaration_list_cannot_be_empty_1123": "Variable declaration list cannot be empty.", "Digit_expected_1124": "Digit expected.", "Hexadecimal_digit_expected_1125": "Hexadecimal digit expected.", "Unexpected_end_of_text_1126": "Unexpected end of text.", "Invalid_character_1127": "Invalid character.", "Declaration_or_statement_expected_1128": "Declaration or statement expected.", "Statement_expected_1129": "Statement expected.", "case_or_default_expected_1130": "'case' or 'default' expected.", "Property_or_signature_expected_1131": "Property or signature expected.", "Enum_member_expected_1132": "Enum member expected.", "Variable_declaration_expected_1134": "Variable declaration expected.", "Argument_expression_expected_1135": "Argument expression expected.", "Property_assignment_expected_1136": "Property assignment expected.", "Expression_or_comma_expected_1137": "Expression or comma expected.", "Parameter_declaration_expected_1138": "Parameter declaration expected.", "Type_parameter_declaration_expected_1139": "Type parameter declaration expected.", "Type_argument_expected_1140": "Type argument expected.", "String_literal_expected_1141": "String literal expected.", "Line_break_not_permitted_here_1142": "Line break not permitted here.", "or_expected_1144": "'{' or ';' expected.", "Declaration_expected_1146": "Declaration expected.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Import declarations in a namespace cannot reference a module.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Cannot use imports, exports, or module augmentations when '--module' is 'none'.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "File name '{0}' differs from already included file name '{1}' only in casing.", "new_T_cannot_be_used_to_create_an_array_Use_new_Array_T_instead_1150": "'new T[]' cannot be used to create an array. Use 'new Array<T>()' instead.", "const_declarations_must_be_initialized_1155": "'const' declarations must be initialized.", "const_declarations_can_only_be_declared_inside_a_block_1156": "'const' declarations can only be declared inside a block.", "let_declarations_can_only_be_declared_inside_a_block_1157": "'let' declarations can only be declared inside a block.", "Unterminated_template_literal_1160": "Unterminated template literal.", "Unterminated_regular_expression_literal_1161": "Unterminated regular expression literal.", "An_object_member_cannot_be_declared_optional_1162": "An object member cannot be declared optional.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "A 'yield' expression is only allowed in a generator body.", "Computed_property_names_are_not_allowed_in_enums_1164": "Computed property names are not allowed in enums.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "A computed property name in an ambient context must refer to an expression whose type is a literal type or a 'unique symbol' type.", "A_computed_property_name_in_a_class_property_declaration_must_refer_to_an_expression_whose_type_is_a_1166": "A computed property name in a class property declaration must refer to an expression whose type is a literal type or a 'unique symbol' type.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "A computed property name in a method overload must refer to an expression whose type is a literal type or a 'unique symbol' type.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "A computed property name in an interface must refer to an expression whose type is a literal type or a 'unique symbol' type.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "A computed property name in a type literal must refer to an expression whose type is a literal type or a 'unique symbol' type.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "A comma expression is not allowed in a computed property name.", "extends_clause_already_seen_1172": "'extends' clause already seen.", "extends_clause_must_precede_implements_clause_1173": "'extends' clause must precede 'implements' clause.", "Classes_can_only_extend_a_single_class_1174": "Classes can only extend a single class.", "implements_clause_already_seen_1175": "'implements' clause already seen.", "Interface_declaration_cannot_have_implements_clause_1176": "Interface declaration cannot have 'implements' clause.", "Binary_digit_expected_1177": "Binary digit expected.", "Octal_digit_expected_1178": "Octal digit expected.", "Unexpected_token_expected_1179": "Unexpected token. '{' expected.", "Property_destructuring_pattern_expected_1180": "Property destructuring pattern expected.", "Array_element_destructuring_pattern_expected_1181": "Array element destructuring pattern expected.", "A_destructuring_declaration_must_have_an_initializer_1182": "A destructuring declaration must have an initializer.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "An implementation cannot be declared in ambient contexts.", "Modifiers_cannot_appear_here_1184": "Modifiers cannot appear here.", "Merge_conflict_marker_encountered_1185": "Merge conflict marker encountered.", "A_rest_element_cannot_have_an_initializer_1186": "A rest element cannot have an initializer.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "A parameter property may not be declared using a binding pattern.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "Only a single variable declaration is allowed in a 'for...of' statement.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "The variable declaration of a 'for...in' statement cannot have an initializer.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "The variable declaration of a 'for...of' statement cannot have an initializer.", "An_import_declaration_cannot_have_modifiers_1191": "An import declaration cannot have modifiers.", "Module_0_has_no_default_export_1192": "Module '{0}' has no default export.", "An_export_declaration_cannot_have_modifiers_1193": "An export declaration cannot have modifiers.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Export declarations are not permitted in a namespace.", "Catch_clause_variable_cannot_have_a_type_annotation_1196": "Catch clause variable cannot have a type annotation.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Catch clause variable cannot have an initializer.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "An extended Unicode escape value must be between 0x0 and 0x10FFFF inclusive.", "Unterminated_Unicode_escape_sequence_1199": "Unterminated Unicode escape sequence.", "Line_terminator_not_permitted_before_arrow_1200": "Line terminator not permitted before arrow.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Import assignment cannot be used when targeting ECMAScript modules. Consider using 'import * as ns from \"mod\"', 'import {a} from \"mod\"', 'import d from \"mod\"', or another module format instead.", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Export assignment cannot be used when targeting ECMAScript modules. Consider using 'export default' or another module format instead.", "Cannot_re_export_a_type_when_the_isolatedModules_flag_is_provided_1205": "Cannot re-export a type when the '--isolatedModules' flag is provided.", "Decorators_are_not_valid_here_1206": "Decorators are not valid here.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Decorators cannot be applied to multiple get/set accessors of the same name.", "Cannot_compile_namespaces_when_the_isolatedModules_flag_is_provided_1208": "Cannot compile namespaces when the '--isolatedModules' flag is provided.", "Ambient_const_enums_are_not_allowed_when_the_isolatedModules_flag_is_provided_1209": "Ambient const enums are not allowed when the '--isolatedModules' flag is provided.", "Invalid_use_of_0_Class_definitions_are_automatically_in_strict_mode_1210": "Invalid use of '{0}'. Class definitions are automatically in strict mode.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "A class declaration without the 'default' modifier must have a name.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Identifier expected. '{0}' is a reserved word in strict mode.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Identifier expected. '{0}' is a reserved word in strict mode. Class definitions are automatically in strict mode.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Identifier expected. '{0}' is a reserved word in strict mode. Modules are automatically in strict mode.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Invalid use of '{0}'. Modules are automatically in strict mode.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Identifier expected. '__esModule' is reserved as an exported marker when transforming ECMAScript modules.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "Export assignment is not supported when '--module' flag is 'system'.", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "Experimental support for decorators is a feature that is subject to change in a future release. Set the 'experimentalDecorators' option to remove this warning.", "Generators_are_only_available_when_targeting_ECMAScript_2015_or_higher_1220": "Generators are only available when targeting ECMAScript 2015 or higher.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Generators are not allowed in an ambient context.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "An overload signature cannot be declared as a generator.", "_0_tag_already_specified_1223": "'{0}' tag already specified.", "Signature_0_must_be_a_type_predicate_1224": "Signature '{0}' must be a type predicate.", "Cannot_find_parameter_0_1225": "Cannot find parameter '{0}'.", "Type_predicate_0_is_not_assignable_to_1_1226": "Type predicate '{0}' is not assignable to '{1}'.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Parameter '{0}' is not in the same position as parameter '{1}'.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "A type predicate is only allowed in return type position for functions and methods.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "A type predicate cannot reference a rest parameter.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "A type predicate cannot reference element '{0}' in a binding pattern.", "An_export_assignment_can_only_be_used_in_a_module_1231": "An export assignment can only be used in a module.", "An_import_declaration_can_only_be_used_in_a_namespace_or_module_1232": "An import declaration can only be used in a namespace or module.", "An_export_declaration_can_only_be_used_in_a_module_1233": "An export declaration can only be used in a module.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "An ambient module declaration is only allowed at the top level in a file.", "A_namespace_declaration_is_only_allowed_in_a_namespace_or_module_1235": "A namespace declaration is only allowed in a namespace or module.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "The return type of a property decorator function must be either 'void' or 'any'.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "The return type of a parameter decorator function must be either 'void' or 'any'.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Unable to resolve signature of class decorator when called as an expression.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Unable to resolve signature of parameter decorator when called as an expression.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Unable to resolve signature of property decorator when called as an expression.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Unable to resolve signature of method decorator when called as an expression.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "'abstract' modifier can only appear on a class, method, or property declaration.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "'{0}' modifier cannot be used with '{1}' modifier.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Abstract methods can only appear within an abstract class.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "Method '{0}' cannot have an implementation because it is marked abstract.", "An_interface_property_cannot_have_an_initializer_1246": "An interface property cannot have an initializer.", "A_type_literal_property_cannot_have_an_initializer_1247": "A type literal property cannot have an initializer.", "A_class_member_cannot_have_the_0_keyword_1248": "A class member cannot have the '{0}' keyword.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "A decorator can only decorate a method implementation, not an overload.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "Function declarations are not allowed inside blocks in strict mode when targeting 'ES3' or 'ES5'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "Function declarations are not allowed inside blocks in strict mode when targeting 'ES3' or 'ES5'. Class definitions are automatically in strict mode.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "Function declarations are not allowed inside blocks in strict mode when targeting 'ES3' or 'ES5'. Modules are automatically in strict mode.", "_0_tag_cannot_be_used_independently_as_a_top_level_JSDoc_tag_1253": "'{0}' tag cannot be used independently as a top level JSDoc tag.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_1254": "A 'const' initializer in an ambient context must be a string or numeric literal.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "A definite assignment assertion '!' is not permitted in this context.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "'with' statements are not allowed in an async function block.", "await_expression_is_only_allowed_within_an_async_function_1308": "'await' expression is only allowed within an async function.", "can_only_be_used_in_an_object_literal_property_inside_a_destructuring_assignment_1312": "'=' can only be used in an object literal property inside a destructuring assignment.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "The body of an 'if' statement cannot be the empty statement.", "Global_module_exports_may_only_appear_in_module_files_1314": "Global module exports may only appear in module files.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Global module exports may only appear in declaration files.", "Global_module_exports_may_only_appear_at_top_level_1316": "Global module exports may only appear at top level.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "A parameter property cannot be declared using a rest parameter.", "An_abstract_accessor_cannot_have_an_implementation_1318": "An abstract accessor cannot have an implementation.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "A default export can only be used in an ECMAScript-style module.", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Type of 'await' operand must either be a valid promise or must not contain a callable 'then' member.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Type of 'yield' operand in an async generator must either be a valid promise or must not contain a callable 'then' member.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Type of iterated elements of a 'yield*' operand must either be a valid promise or must not contain a callable 'then' member.", "Dynamic_import_cannot_be_used_when_targeting_ECMAScript_2015_modules_1323": "Dynamic import cannot be used when targeting ECMAScript 2015 modules.", "Dynamic_import_must_have_one_specifier_as_an_argument_1324": "Dynamic import must have one specifier as an argument.", "Specifier_of_dynamic_import_cannot_be_spread_element_1325": "Specifier of dynamic import cannot be spread element.", "Dynamic_import_cannot_have_type_arguments_1326": "Dynamic import cannot have type arguments", "String_literal_with_double_quotes_expected_1327": "String literal with double quotes expected.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Property value can only be string literal, numeric literal, 'true', 'false', 'null', object literal or array literal.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' accepts too few arguments to be used as a decorator here. Did you mean to call it first and write '@{0}()'?", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "A property of an interface or type literal whose type is a 'unique symbol' type must be 'readonly'.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "A property of a class whose type is a 'unique symbol' type must be both 'static' and 'readonly'.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "A variable whose type is a 'unique symbol' type must be 'const'.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "'unique symbol' types may not be used on a variable declaration with a binding name.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "'unique symbol' types are only allowed on variables in a variable statement.", "unique_symbol_types_are_not_allowed_here_1335": "'unique symbol' types are not allowed here.", "An_index_signature_parameter_type_cannot_be_a_type_alias_Consider_writing_0_Colon_1_Colon_2_instead_1336": "An index signature parameter type cannot be a type alias. Consider writing '[{0}: {1}]: {2}' instead.", "An_index_signature_parameter_type_cannot_be_a_union_type_Consider_using_a_mapped_object_type_instead_1337": "An index signature parameter type cannot be a union type. Consider using a mapped object type instead.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "'infer' declarations are only permitted in the 'extends' clause of a conditional type.", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "Module '{0}' does not refer to a value, but is used as a value here.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_1340": "Module '{0}' does not refer to a type, but is used as a type here.", "Type_arguments_cannot_be_used_here_1342": "Type arguments cannot be used here.", "The_import_meta_meta_property_is_only_allowed_using_ESNext_for_the_target_and_module_compiler_option_1343": "The 'import.meta' meta-property is only allowed using 'ESNext' for the 'target' and 'module' compiler options.", "Duplicate_identifier_0_2300": "Duplicate identifier '{0}'.", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "Initializer of instance member variable '{0}' cannot reference identifier '{1}' declared in the constructor.", "Static_members_cannot_reference_class_type_parameters_2302": "Static members cannot reference class type parameters.", "Circular_definition_of_import_alias_0_2303": "Circular definition of import alias '{0}'.", "Cannot_find_name_0_2304": "Cannot find name '{0}'.", "Module_0_has_no_exported_member_1_2305": "Module '{0}' has no exported member '{1}'.", "File_0_is_not_a_module_2306": "File '{0}' is not a module.", "Cannot_find_module_0_2307": "Cannot find module '{0}'.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Module {0} has already exported a member named '{1}'. Consider explicitly re-exporting to resolve the ambiguity.", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "An export assignment cannot be used in a module with other exported elements.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Type '{0}' recursively references itself as a base type.", "A_class_may_only_extend_another_class_2311": "A class may only extend another class.", "An_interface_may_only_extend_a_class_or_another_interface_2312": "An interface may only extend a class or another interface.", "Type_parameter_0_has_a_circular_constraint_2313": "Type parameter '{0}' has a circular constraint.", "Generic_type_0_requires_1_type_argument_s_2314": "Generic type '{0}' requires {1} type argument(s).", "Type_0_is_not_generic_2315": "Type '{0}' is not generic.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Global type '{0}' must be a class or interface type.", "Global_type_0_must_have_1_type_parameter_s_2317": "Global type '{0}' must have {1} type parameter(s).", "Cannot_find_global_type_0_2318": "Cannot find global type '{0}'.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Named property '{0}' of types '{1}' and '{2}' are not identical.", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "Interface '{0}' cannot simultaneously extend types '{1}' and '{2}'.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Excessive stack depth comparing types '{0}' and '{1}'.", "Type_0_is_not_assignable_to_type_1_2322": "Type '{0}' is not assignable to type '{1}'.", "Cannot_redeclare_exported_variable_0_2323": "Cannot redeclare exported variable '{0}'.", "Property_0_is_missing_in_type_1_2324": "Property '{0}' is missing in type '{1}'.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "Property '{0}' is private in type '{1}' but not in type '{2}'.", "Types_of_property_0_are_incompatible_2326": "Types of property '{0}' are incompatible.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "Property '{0}' is optional in type '{1}' but required in type '{2}'.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Types of parameters '{0}' and '{1}' are incompatible.", "Index_signature_is_missing_in_type_0_2329": "Index signature is missing in type '{0}'.", "Index_signatures_are_incompatible_2330": "Index signatures are incompatible.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "'this' cannot be referenced in a module or namespace body.", "this_cannot_be_referenced_in_current_location_2332": "'this' cannot be referenced in current location.", "this_cannot_be_referenced_in_constructor_arguments_2333": "'this' cannot be referenced in constructor arguments.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "'this' cannot be referenced in a static property initializer.", "super_can_only_be_referenced_in_a_derived_class_2335": "'super' can only be referenced in a derived class.", "super_cannot_be_referenced_in_constructor_arguments_2336": "'super' cannot be referenced in constructor arguments.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Super calls are not permitted outside constructors or in nested functions inside constructors.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "'super' property access is permitted only in a constructor, member function, or member accessor of a derived class.", "Property_0_does_not_exist_on_type_1_2339": "Property '{0}' does not exist on type '{1}'.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Only public and protected methods of the base class are accessible via the 'super' keyword.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "Property '{0}' is private and only accessible within class '{1}'.", "An_index_expression_argument_must_be_of_type_string_number_symbol_or_any_2342": "An index expression argument must be of type 'string', 'number', 'symbol', or 'any'.", "This_syntax_requires_an_imported_helper_named_1_but_module_0_has_no_exported_member_1_2343": "This syntax requires an imported helper named '{1}', but module '{0}' has no exported member '{1}'.", "Type_0_does_not_satisfy_the_constraint_1_2344": "Type '{0}' does not satisfy the constraint '{1}'.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "Argument of type '{0}' is not assignable to parameter of type '{1}'.", "Call_target_does_not_contain_any_signatures_2346": "Call target does not contain any signatures.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Untyped function calls may not accept type arguments.", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Value of type '{0}' is not callable. Did you mean to include 'new'?", "Cannot_invoke_an_expression_whose_type_lacks_a_call_signature_Type_0_has_no_compatible_call_signatur_2349": "Cannot invoke an expression whose type lacks a call signature. Type '{0}' has no compatible call signatures.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Only a void function can be called with the 'new' keyword.", "Cannot_use_new_with_an_expression_whose_type_lacks_a_call_or_construct_signature_2351": "Cannot use 'new' with an expression whose type lacks a call or construct signature.", "Type_0_cannot_be_converted_to_type_1_2352": "Type '{0}' cannot be converted to type '{1}'.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Object literal may only specify known properties, and '{0}' does not exist in type '{1}'.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "This syntax requires an imported helper but module '{0}' cannot be found.", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "A function whose declared type is neither 'void' nor 'any' must return a value.", "An_arithmetic_operand_must_be_of_type_any_number_or_an_enum_type_2356": "An arithmetic operand must be of type 'any', 'number' or an enum type.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "The operand of an increment or decrement operator must be a variable or a property access.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "The left-hand side of an 'instanceof' expression must be of type 'any', an object type or a type parameter.", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "The right-hand side of an 'instanceof' expression must be of type 'any' or of a type assignable to the 'Function' interface type.", "The_left_hand_side_of_an_in_expression_must_be_of_type_any_string_number_or_symbol_2360": "The left-hand side of an 'in' expression must be of type 'any', 'string', 'number', or 'symbol'.", "The_right_hand_side_of_an_in_expression_must_be_of_type_any_an_object_type_or_a_type_parameter_2361": "The right-hand side of an 'in' expression must be of type 'any', an object type or a type parameter.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_or_an_enum_type_2362": "The left-hand side of an arithmetic operation must be of type 'any', 'number' or an enum type.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_or_an_enum_type_2363": "The right-hand side of an arithmetic operation must be of type 'any', 'number' or an enum type.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "The left-hand side of an assignment expression must be a variable or a property access.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Operator '{0}' cannot be applied to types '{1}' and '{2}'.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Function lacks ending return statement and return type does not include 'undefined'.", "Type_parameter_name_cannot_be_0_2368": "Type parameter name cannot be '{0}'.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "A parameter property is only allowed in a constructor implementation.", "A_rest_parameter_must_be_of_an_array_type_2370": "A rest parameter must be of an array type.", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "A parameter initializer is only allowed in a function or constructor implementation.", "Parameter_0_cannot_be_referenced_in_its_initializer_2372": "Parameter '{0}' cannot be referenced in its initializer.", "Initializer_of_parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Initializer of parameter '{0}' cannot reference identifier '{1}' declared after it.", "Duplicate_string_index_signature_2374": "Duplicate string index signature.", "Duplicate_number_index_signature_2375": "Duplicate number index signature.", "A_super_call_must_be_the_first_statement_in_the_constructor_when_a_class_contains_initialized_proper_2376": "A 'super' call must be the first statement in the constructor when a class contains initialized properties or has parameter properties.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Constructors for derived classes must contain a 'super' call.", "A_get_accessor_must_return_a_value_2378": "A 'get' accessor must return a value.", "Getter_and_setter_accessors_do_not_agree_in_visibility_2379": "Getter and setter accessors do not agree in visibility.", "get_and_set_accessor_must_have_the_same_type_2380": "'get' and 'set' accessor must have the same type.", "A_signature_with_an_implementation_cannot_use_a_string_literal_type_2381": "A signature with an implementation cannot use a string literal type.", "Specialized_overload_signature_is_not_assignable_to_any_non_specialized_signature_2382": "Specialized overload signature is not assignable to any non-specialized signature.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Overload signatures must all be exported or non-exported.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Overload signatures must all be ambient or non-ambient.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Overload signatures must all be public, private or protected.", "Overload_signatures_must_all_be_optional_or_required_2386": "Overload signatures must all be optional or required.", "Function_overload_must_be_static_2387": "Function overload must be static.", "Function_overload_must_not_be_static_2388": "Function overload must not be static.", "Function_implementation_name_must_be_0_2389": "Function implementation name must be '{0}'.", "Constructor_implementation_is_missing_2390": "Constructor implementation is missing.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Function implementation is missing or not immediately following the declaration.", "Multiple_constructor_implementations_are_not_allowed_2392": "Multiple constructor implementations are not allowed.", "Duplicate_function_implementation_2393": "Duplicate function implementation.", "Overload_signature_is_not_compatible_with_function_implementation_2394": "Overload signature is not compatible with function implementation.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Individual declarations in merged declaration '{0}' must be all exported or all local.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Duplicate identifier 'arguments'. Compiler uses 'arguments' to initialize rest parameters.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Declaration name conflicts with built-in global identifier '{0}'.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Duplicate identifier '_this'. Compiler uses variable declaration '_this' to capture 'this' reference.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "Expression resolves to variable declaration '_this' that compiler uses to capture 'this' reference.", "Duplicate_identifier_super_Compiler_uses_super_to_capture_base_class_reference_2401": "Duplicate identifier '_super'. Compiler uses '_super' to capture base class reference.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "Expression resolves to '_super' that compiler uses to capture base class reference.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Subsequent variable declarations must have the same type.  Variable '{0}' must be of type '{1}', but here has type '{2}'.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "The left-hand side of a 'for...in' statement cannot use a type annotation.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "The left-hand side of a 'for...in' statement must be of type 'string' or 'any'.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "The left-hand side of a 'for...in' statement must be a variable or a property access.", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "The right-hand side of a 'for...in' statement must be of type 'any', an object type or a type parameter, but here has type '{0}'.", "Setters_cannot_return_a_value_2408": "Setters cannot return a value.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Return type of constructor signature must be assignable to the instance type of the class.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "The 'with' statement is not supported. All symbols in a 'with' block will have type 'any'.", "Property_0_of_type_1_is_not_assignable_to_string_index_type_2_2411": "Property '{0}' of type '{1}' is not assignable to string index type '{2}'.", "Property_0_of_type_1_is_not_assignable_to_numeric_index_type_2_2412": "Property '{0}' of type '{1}' is not assignable to numeric index type '{2}'.", "Numeric_index_type_0_is_not_assignable_to_string_index_type_1_2413": "Numeric index type '{0}' is not assignable to string index type '{1}'.", "Class_name_cannot_be_0_2414": "Class name cannot be '{0}'.", "Class_0_incorrectly_extends_base_class_1_2415": "Class '{0}' incorrectly extends base class '{1}'.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "Property '{0}' in type '{1}' is not assignable to the same property in base type '{2}'.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Class static side '{0}' incorrectly extends base class static side '{1}'.", "Class_0_incorrectly_implements_interface_1_2420": "Class '{0}' incorrectly implements interface '{1}'.", "A_class_may_only_implement_another_class_or_interface_2422": "A class may only implement another class or interface.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "Class '{0}' defines instance member function '{1}', but extended class '{2}' defines it as instance member accessor.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_proper_2424": "Class '{0}' defines instance member function '{1}', but extended class '{2}' defines it as instance member property.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "Class '{0}' defines instance member property '{1}', but extended class '{2}' defines it as instance member function.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "Class '{0}' defines instance member accessor '{1}', but extended class '{2}' defines it as instance member function.", "Interface_name_cannot_be_0_2427": "Interface name cannot be '{0}'.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "All declarations of '{0}' must have identical type parameters.", "Interface_0_incorrectly_extends_interface_1_2430": "Interface '{0}' incorrectly extends interface '{1}'.", "Enum_name_cannot_be_0_2431": "Enum name cannot be '{0}'.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "In an enum with multiple declarations, only one declaration can omit an initializer for its first enum element.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "A namespace declaration cannot be in a different file from a class or function with which it is merged.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "A namespace declaration cannot be located prior to a class or function with which it is merged.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Ambient modules cannot be nested in other modules or namespaces.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Ambient module declaration cannot specify relative module name.", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "Module '{0}' is hidden by a local declaration with the same name.", "Import_name_cannot_be_0_2438": "Import name cannot be '{0}'.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "Import or export declaration in an ambient module declaration cannot reference module through relative module name.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "Import declaration conflicts with local declaration of '{0}'.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Duplicate identifier '{0}'. Compiler reserves name '{1}' in top level scope of a module.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Types have separate declarations of a private property '{0}'.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "Property '{0}' is protected but type '{1}' is not a class derived from '{2}'.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "Property '{0}' is protected in type '{1}' but public in type '{2}'.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "Property '{0}' is protected and only accessible within class '{1}' and its subclasses.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_2446": "Property '{0}' is protected and only accessible through an instance of class '{1}'.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "The '{0}' operator is not allowed for boolean types. Consider using '{1}' instead.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Block-scoped variable '{0}' used before its declaration.", "Class_0_used_before_its_declaration_2449": "Class '{0}' used before its declaration.", "Enum_0_used_before_its_declaration_2450": "Enum '{0}' used before its declaration.", "Cannot_redeclare_block_scoped_variable_0_2451": "Cannot redeclare block-scoped variable '{0}'.", "An_enum_member_cannot_have_a_numeric_name_2452": "An enum member cannot have a numeric name.", "The_type_argument_for_type_parameter_0_cannot_be_inferred_from_the_usage_Consider_specifying_the_typ_2453": "The type argument for type parameter '{0}' cannot be inferred from the usage. Consider specifying the type arguments explicitly.", "Variable_0_is_used_before_being_assigned_2454": "Variable '{0}' is used before being assigned.", "Type_argument_candidate_1_is_not_a_valid_type_argument_because_it_is_not_a_supertype_of_candidate_0_2455": "Type argument candidate '{1}' is not a valid type argument because it is not a supertype of candidate '{0}'.", "Type_alias_0_circularly_references_itself_2456": "Type alias '{0}' circularly references itself.", "Type_alias_name_cannot_be_0_2457": "Type alias name cannot be '{0}'.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "An AMD module cannot have multiple name assignments.", "Type_0_has_no_property_1_and_no_string_index_signature_2459": "Type '{0}' has no property '{1}' and no string index signature.", "Type_0_has_no_property_1_2460": "Type '{0}' has no property '{1}'.", "Type_0_is_not_an_array_type_2461": "Type '{0}' is not an array type.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "A rest element must be last in a destructuring pattern.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "A binding pattern parameter cannot be optional in an implementation signature.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "A computed property name must be of type 'string', 'number', 'symbol', or 'any'.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "'this' cannot be referenced in a computed property name.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "'super' cannot be referenced in a computed property name.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "A computed property name cannot reference a type parameter from its containing type.", "Cannot_find_global_value_0_2468": "Cannot find global value '{0}'.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "The '{0}' operator cannot be applied to type 'symbol'.", "Symbol_reference_does_not_refer_to_the_global_Symbol_constructor_object_2470": "'Symbol' reference does not refer to the global Symbol constructor object.", "A_computed_property_name_of_the_form_0_must_be_of_type_symbol_2471": "A computed property name of the form '{0}' must be of type 'symbol'.", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "Spread operator in 'new' expressions is only available when targeting ECMAScript 5 and higher.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Enum declarations must all be const or non-const.", "In_const_enum_declarations_member_initializer_must_be_constant_expression_2474": "In 'const' enum declarations member initializer must be constant expression.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "'const' enums can only be used in property or index access expressions or the right hand side of an import declaration or export assignment or type query.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "A const enum member can only be accessed using a string literal.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "'const' enum member initializer was evaluated to a non-finite value.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "'const' enum member initializer was evaluated to disallowed value 'NaN'.", "Property_0_does_not_exist_on_const_enum_1_2479": "Property '{0}' does not exist on 'const' enum '{1}'.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let' is not allowed to be used as a name in 'let' or 'const' declarations.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Cannot initialize outer scoped variable '{0}' in the same scope as block scoped declaration '{1}'.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "The left-hand side of a 'for...of' statement cannot use a type annotation.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "Export declaration conflicts with exported declaration of '{0}'.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "The left-hand side of a 'for...of' statement must be a variable or a property access.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Type '{0}' must have a '[Symbol.iterator]()' method that returns an iterator.", "An_iterator_must_have_a_next_method_2489": "An iterator must have a 'next()' method.", "The_type_returned_by_the_next_method_of_an_iterator_must_have_a_value_property_2490": "The type returned by the 'next()' method of an iterator must have a 'value' property.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "The left-hand side of a 'for...in' statement cannot be a destructuring pattern.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Cannot redeclare identifier '{0}' in catch clause.", "Tuple_type_0_with_length_1_cannot_be_assigned_to_tuple_with_length_2_2493": "Tuple type '{0}' with length '{1}' cannot be assigned to tuple with length '{2}'.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "Using a string in a 'for...of' statement is only supported in ECMAScript 5 and higher.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Type '{0}' is not an array type or a string type.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "The 'arguments' object cannot be referenced in an arrow function in ES3 and ES5. Consider using a standard function expression.", "Module_0_resolves_to_a_non_module_entity_and_cannot_be_imported_using_this_construct_2497": "Module '{0}' resolves to a non-module entity and cannot be imported using this construct.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Module '{0}' uses 'export =' and cannot be used with 'export *'.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "An interface can only extend an identifier/qualified-name with optional type arguments.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "A class can only implement an identifier/qualified-name with optional type arguments.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "A rest element cannot contain a binding pattern.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' is referenced directly or indirectly in its own type annotation.", "Cannot_find_namespace_0_2503": "Cannot find namespace '{0}'.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Type '{0}' must have a '[Symbol.asyncIterator]()' method that returns an async iterator.", "A_generator_cannot_have_a_void_type_annotation_2505": "A generator cannot have a 'void' type annotation.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' is referenced directly or indirectly in its own base expression.", "Type_0_is_not_a_constructor_function_type_2507": "Type '{0}' is not a constructor function type.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "No base constructor has the specified number of type arguments.", "Base_constructor_return_type_0_is_not_a_class_or_interface_type_2509": "Base constructor return type '{0}' is not a class or interface type.", "Base_constructors_must_all_have_the_same_return_type_2510": "Base constructors must all have the same return type.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Cannot create an instance of an abstract class.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Overload signatures must all be abstract or non-abstract.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "Abstract method '{0}' in class '{1}' cannot be accessed via super expression.", "Classes_containing_abstract_methods_must_be_marked_abstract_2514": "Classes containing abstract methods must be marked abstract.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "Non-abstract class '{0}' does not implement inherited abstract member '{1}' from class '{2}'.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "All declarations of an abstract method must be consecutive.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Cannot assign an abstract constructor type to a non-abstract constructor type.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "A 'this'-based type guard is not compatible with a parameter-based type guard.", "An_async_iterator_must_have_a_next_method_2519": "An async iterator must have a 'next()' method.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Duplicate identifier '{0}'. Compiler uses declaration '{1}' to support async functions.", "Expression_resolves_to_variable_declaration_0_that_compiler_uses_to_support_async_functions_2521": "Expression resolves to variable declaration '{0}' that compiler uses to support async functions.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "The 'arguments' object cannot be referenced in an async function or method in ES3 and ES5. Consider using a standard function or method.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "'yield' expressions cannot be used in a parameter initializer.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "'await' expressions cannot be used in a parameter initializer.", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "Initializer provides no value for this binding element and the binding element has no default value.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "A 'this' type is available only in a non-static member of a class or interface.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "The inferred type of '{0}' references an inaccessible '{1}' type. A type annotation is necessary.", "A_module_cannot_have_multiple_default_exports_2528": "A module cannot have multiple default exports.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Duplicate identifier '{0}'. Compiler reserves name '{1}' in top level scope of a module containing async functions.", "Property_0_is_incompatible_with_index_signature_2530": "Property '{0}' is incompatible with index signature.", "Object_is_possibly_null_2531": "Object is possibly 'null'.", "Object_is_possibly_undefined_2532": "Object is possibly 'undefined'.", "Object_is_possibly_null_or_undefined_2533": "Object is possibly 'null' or 'undefined'.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "A function returning 'never' cannot have a reachable end point.", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "Enum type '{0}' has members with initializers that are not literals.", "Type_0_cannot_be_used_to_index_type_1_2536": "Type '{0}' cannot be used to index type '{1}'.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "Type '{0}' has no matching index signature for type '{1}'.", "Type_0_cannot_be_used_as_an_index_type_2538": "Type '{0}' cannot be used as an index type.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Cannot assign to '{0}' because it is not a variable.", "Cannot_assign_to_0_because_it_is_a_constant_or_a_read_only_property_2540": "Cannot assign to '{0}' because it is a constant or a read-only property.", "The_target_of_an_assignment_must_be_a_variable_or_a_property_access_2541": "The target of an assignment must be a variable or a property access.", "Index_signature_in_type_0_only_permits_reading_2542": "Index signature in type '{0}' only permits reading.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Duplicate identifier '_newTarget'. Compiler uses variable declaration '_newTarget' to capture 'new.target' meta-property reference.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "Expression resolves to variable declaration '_newTarget' that compiler uses to capture 'new.target' meta-property reference.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "A mixin class must have a constructor with a single rest parameter of type 'any[]'.", "Property_0_has_conflicting_declarations_and_is_inaccessible_in_type_1_2546": "Property '{0}' has conflicting declarations and is inaccessible in type '{1}'.", "The_type_returned_by_the_next_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value__2547": "The type returned by the 'next()' method of an async iterator must be a promise for a type with a 'value' property.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "Type '{0}' is not an array type or does not have a '[Symbol.iterator]()' method that returns an iterator.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "Type '{0}' is not an array type or a string type or does not have a '[Symbol.iterator]()' method that returns an iterator.", "Generic_type_instantiation_is_excessively_deep_and_possibly_infinite_2550": "Generic type instantiation is excessively deep and possibly infinite.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "Property '{0}' does not exist on type '{1}'. Did you mean '{2}'?", "Cannot_find_name_0_Did_you_mean_1_2552": "Cannot find name '{0}'. Did you mean '{1}'?", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "Computed values are not permitted in an enum with string valued members.", "Expected_0_arguments_but_got_1_2554": "Expected {0} arguments, but got {1}.", "Expected_at_least_0_arguments_but_got_1_2555": "Expected at least {0} arguments, but got {1}.", "Expected_0_arguments_but_got_1_or_more_2556": "Expected {0} arguments, but got {1} or more.", "Expected_at_least_0_arguments_but_got_1_or_more_2557": "Expected at least {0} arguments, but got {1} or more.", "Expected_0_type_arguments_but_got_1_2558": "Expected {0} type arguments, but got {1}.", "Type_0_has_no_properties_in_common_with_type_1_2559": "Type '{0}' has no properties in common with type '{1}'.", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "Value of type '{0}' has no properties in common with type '{1}'. Did you mean to call it?", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Object literal may only specify known properties, but '{0}' does not exist in type '{1}'. Did you mean to write '{2}'?", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Base class expressions cannot reference class type parameters.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "The containing function or module body is too large for control flow analysis.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "Property '{0}' has no initializer and is not definitely assigned in the constructor.", "Property_0_is_used_before_being_assigned_2565": "Property '{0}' is used before being assigned.", "A_rest_element_cannot_have_a_property_name_2566": "A rest element cannot have a property name.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Enum declarations can only merge with namespace or other enum declarations.", "Type_0_is_not_an_array_type_Use_compiler_option_downlevelIteration_to_allow_iterating_of_iterators_2568": "Type '{0}' is not an array type. Use compiler option '--downlevelIteration' to allow iterating of iterators.", "Type_0_is_not_an_array_type_or_a_string_type_Use_compiler_option_downlevelIteration_to_allow_iterati_2569": "Type '{0}' is not an array type or a string type. Use compiler option '--downlevelIteration' to allow iterating of iterators.", "Property_0_does_not_exist_on_type_1_Did_you_forget_to_use_await_2570": "Property '{0}' does not exist on type '{1}'. Did you forget to use 'await'?", "JSX_element_attributes_type_0_may_not_be_a_union_type_2600": "JSX element attributes type '{0}' may not be a union type.", "The_return_type_of_a_JSX_element_constructor_must_return_an_object_type_2601": "The return type of a JSX element constructor must return an object type.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "JSX element implicitly has type 'any' because the global type 'JSX.Element' does not exist.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "Property '{0}' in type '{1}' is not assignable to type '{2}'.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX element type '{0}' does not have any construct or call signatures.", "JSX_element_type_0_is_not_a_constructor_function_for_JSX_elements_2605": "JSX element type '{0}' is not a constructor function for JSX elements.", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "Property '{0}' of JSX spread attribute is not assignable to target property.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "JSX element class does not support attributes because it does not have a '{0}' property.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "The global type 'JSX.{0}' may not have more than one property.", "JSX_spread_child_must_be_an_array_type_2609": "JSX spread child must be an array type.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Cannot augment module '{0}' with value exports because it resolves to a non-module entity.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "A member initializer in a enum declaration cannot reference members declared after it, including members defined in other enums.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "Merged declaration '{0}' cannot include a default export declaration. Consider adding a separate 'export default {0}' declaration instead.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Non-abstract class expression does not implement inherited abstract member '{0}' from class '{1}'.", "Exported_external_package_typings_file_cannot_contain_tripleslash_references_Please_contact_the_pack_2654": "Exported external package typings file cannot contain tripleslash references. Please contact the package author to update the package definition.", "Exported_external_package_typings_file_0_is_not_a_module_Please_contact_the_package_author_to_update_2656": "Exported external package typings file '{0}' is not a module. Please contact the package author to update the package definition.", "JSX_expressions_must_have_one_parent_element_2657": "JSX expressions must have one parent element.", "Type_0_provides_no_match_for_the_signature_1_2658": "Type '{0}' provides no match for the signature '{1}'.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "'super' is only allowed in members of object literal expressions when option 'target' is 'ES2015' or higher.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "'super' can only be referenced in members of derived classes or object literal expressions.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Cannot export '{0}'. Only local declarations can be exported from a module.", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Cannot find name '{0}'. Did you mean the static member '{1}.{0}'?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Cannot find name '{0}'. Did you mean the instance member 'this.{0}'?", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Invalid module name in augmentation, module '{0}' cannot be found.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Invalid module name in augmentation. Module '{0}' resolves to an untyped module at '{1}', which cannot be augmented.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Exports and export assignments are not permitted in module augmentations.", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Imports are not permitted in module augmentations. Consider moving them to the enclosing external module.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "'export' modifier cannot be applied to ambient modules and module augmentations since they are always visible.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Augmentations for the global scope can only be directly nested in external modules or ambient module declarations.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Augmentations for the global scope should have 'declare' modifier unless they appear in already ambient context.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Cannot augment module '{0}' because it resolves to a non-module entity.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Cannot assign a '{0}' constructor type to a '{1}' constructor type.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Constructor of class '{0}' is private and only accessible within the class declaration.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Constructor of class '{0}' is protected and only accessible within the class declaration.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Cannot extend a class '{0}'. Class constructor is marked as private.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Accessors must both be abstract or non-abstract.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "A type predicate's type must be assignable to its parameter's type.", "Type_0_is_not_comparable_to_type_1_2678": "Type '{0}' is not comparable to type '{1}'.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "A function that is called with the 'new' keyword cannot have a 'this' type that is 'void'.", "A_0_parameter_must_be_the_first_parameter_2680": "A '{0}' parameter must be the first parameter.", "A_constructor_cannot_have_a_this_parameter_2681": "A constructor cannot have a 'this' parameter.", "get_and_set_accessor_must_have_the_same_this_type_2682": "'get' and 'set' accessor must have the same 'this' type.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' implicitly has type 'any' because it does not have a type annotation.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "The 'this' context of type '{0}' is not assignable to method's 'this' of type '{1}'.", "The_this_types_of_each_signature_are_incompatible_2685": "The 'this' types of each signature are incompatible.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "All_declarations_of_0_must_have_identical_modifiers_2687": "All declarations of '{0}' must have identical modifiers.", "Cannot_find_type_definition_file_for_0_2688": "Cannot find type definition file for '{0}'.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Cannot extend an interface '{0}'. Did you mean 'implements'?", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "An import path cannot end with a '{0}' extension. Consider importing '{1}' instead.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' is a primitive, but '{1}' is a wrapper object. Prefer using '{0}' when possible.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' only refers to a type, but is being used as a value here.", "Namespace_0_has_no_exported_member_1_2694": "Namespace '{0}' has no exported member '{1}'.", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Left side of comma operator is unused and has no side effects.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "The 'Object' type is assignable to very few other types. Did you mean to use the 'any' type instead?", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "An async function or method must return a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your `--lib` option.", "Spread_types_may_only_be_created_from_object_types_2698": "Spread types may only be created from object types.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "Static property '{0}' conflicts with built-in property 'Function.{0}' of constructor function '{1}'.", "Rest_types_may_only_be_created_from_object_types_2700": "Rest types may only be created from object types.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "The target of an object rest assignment must be a variable or a property access.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' only refers to a type, but is being used as a namespace here.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "The operand of a delete operator must be a property reference.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "The operand of a delete operator cannot be a read-only property.", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "An async function or method in ES5/ES3 requires the 'Promise' constructor.  Make sure you have a declaration for the 'Promise' constructor or include 'ES2015' in your `--lib` option.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Required type parameters may not follow optional type parameters.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Generic type '{0}' requires between {1} and {2} type arguments.", "Cannot_use_namespace_0_as_a_value_2708": "Cannot use namespace '{0}' as a value.", "Cannot_use_namespace_0_as_a_type_2709": "Cannot use namespace '{0}' as a type.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}' are specified twice. The attribute named '{0}' will be overwritten.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your `--lib` option.", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "A dynamic import call in ES5/ES3 requires the 'Promise' constructor.  Make sure you have a declaration for the 'Promise' constructor or include 'ES2015' in your `--lib` option.", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Cannot access '{0}.{1}' because '{0}' is a type, but not a namespace. Did you mean to retrieve the type of the property '{1}' in '{0}' with '{0}[\"{1}\"]'?", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "The expression of an export assignment must be an identifier or qualified name in an ambient context.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "Abstract property '{0}' in class '{1}' cannot be accessed in the constructor.", "Type_parameter_0_has_a_circular_default_2716": "Type parameter '{0}' has a circular default.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Subsequent property declarations must have the same type.  Property '{0}' must be of type '{1}', but here has type '{2}'.", "Duplicate_declaration_0_2718": "Duplicate declaration '{0}'.", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Type '{0}' is not assignable to type '{1}'. Two different types with this name exist, but they are unrelated.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "Class '{0}' incorrectly implements class '{1}'. Did you mean to extend '{1}' and inherit its members as a subclass?", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Cannot invoke an object which is possibly 'null'.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Cannot invoke an object which is possibly 'undefined'.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Cannot invoke an object which is possibly 'null' or 'undefined'.", "Module_0_has_no_exported_member_1_Did_you_mean_2_2724": "Module '{0}' has no exported member '{1}'. Did you mean '{2}'?", "Import_declaration_0_is_using_private_name_1_4000": "Import declaration '{0}' is using private name '{1}'.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Type parameter '{0}' of exported class has or is using private name '{1}'.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Type parameter '{0}' of exported interface has or is using private name '{1}'.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Type parameter '{0}' of constructor signature from exported interface has or is using private name '{1}'.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Type parameter '{0}' of call signature from exported interface has or is using private name '{1}'.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Type parameter '{0}' of public static method from exported class has or is using private name '{1}'.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Type parameter '{0}' of public method from exported class has or is using private name '{1}'.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Type parameter '{0}' of method from exported interface has or is using private name '{1}'.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Type parameter '{0}' of exported function has or is using private name '{1}'.", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "Implements clause of exported class '{0}' has or is using private name '{1}'.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "'extends' clause of exported class '{0}' has or is using private name '{1}'.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "'extends' clause of exported interface '{0}' has or is using private name '{1}'.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "Exported variable '{0}' has or is using name '{1}' from external module {2} but cannot be named.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "Exported variable '{0}' has or is using name '{1}' from private module '{2}'.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "Exported variable '{0}' has or is using private name '{1}'.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "Public static property '{0}' of exported class has or is using name '{1}' from external module {2} but cannot be named.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "Public static property '{0}' of exported class has or is using name '{1}' from private module '{2}'.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "Public static property '{0}' of exported class has or is using private name '{1}'.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "Public property '{0}' of exported class has or is using name '{1}' from external module {2} but cannot be named.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "Public property '{0}' of exported class has or is using name '{1}' from private module '{2}'.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "Public property '{0}' of exported class has or is using private name '{1}'.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "Property '{0}' of exported interface has or is using name '{1}' from private module '{2}'.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "Property '{0}' of exported interface has or is using private name '{1}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Parameter type of public static setter '{0}' from exported class has or is using name '{1}' from private module '{2}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Parameter type of public static setter '{0}' from exported class has or is using private name '{1}'.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Parameter type of public setter '{0}' from exported class has or is using name '{1}' from private module '{2}'.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Parameter type of public setter '{0}' from exported class has or is using private name '{1}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Return type of public static getter '{0}' from exported class has or is using name '{1}' from external module {2} but cannot be named.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Return type of public static getter '{0}' from exported class has or is using name '{1}' from private module '{2}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Return type of public static getter '{0}' from exported class has or is using private name '{1}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Return type of public getter '{0}' from exported class has or is using name '{1}' from external module {2} but cannot be named.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Return type of public getter '{0}' from exported class has or is using name '{1}' from private module '{2}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Return type of public getter '{0}' from exported class has or is using private name '{1}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Return type of constructor signature from exported interface has or is using name '{0}' from private module '{1}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Return type of constructor signature from exported interface has or is using private name '{0}'.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Return type of call signature from exported interface has or is using name '{0}' from private module '{1}'.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Return type of call signature from exported interface has or is using private name '{0}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Return type of index signature from exported interface has or is using name '{0}' from private module '{1}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Return type of index signature from exported interface has or is using private name '{0}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Return type of public static method from exported class has or is using name '{0}' from external module {1} but cannot be named.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Return type of public static method from exported class has or is using name '{0}' from private module '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Return type of public static method from exported class has or is using private name '{0}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Return type of public method from exported class has or is using name '{0}' from external module {1} but cannot be named.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Return type of public method from exported class has or is using name '{0}' from private module '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Return type of public method from exported class has or is using private name '{0}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Return type of method from exported interface has or is using name '{0}' from private module '{1}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Return type of method from exported interface has or is using private name '{0}'.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Return type of exported function has or is using name '{0}' from external module {1} but cannot be named.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Return type of exported function has or is using name '{0}' from private module '{1}'.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Return type of exported function has or is using private name '{0}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Parameter '{0}' of constructor from exported class has or is using name '{1}' from external module {2} but cannot be named.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Parameter '{0}' of constructor from exported class has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Parameter '{0}' of constructor from exported class has or is using private name '{1}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Parameter '{0}' of constructor signature from exported interface has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Parameter '{0}' of constructor signature from exported interface has or is using private name '{1}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Parameter '{0}' of call signature from exported interface has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Parameter '{0}' of call signature from exported interface has or is using private name '{1}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Parameter '{0}' of public static method from exported class has or is using name '{1}' from external module {2} but cannot be named.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Parameter '{0}' of public static method from exported class has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Parameter '{0}' of public static method from exported class has or is using private name '{1}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Parameter '{0}' of public method from exported class has or is using name '{1}' from external module {2} but cannot be named.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Parameter '{0}' of public method from exported class has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Parameter '{0}' of public method from exported class has or is using private name '{1}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Parameter '{0}' of method from exported interface has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Parameter '{0}' of method from exported interface has or is using private name '{1}'.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Parameter '{0}' of exported function has or is using name '{1}' from external module {2} but cannot be named.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Parameter '{0}' of exported function has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Parameter '{0}' of exported function has or is using private name '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "Exported type alias '{0}' has or is using private name '{1}'.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "Default export of the module has or is using private name '{0}'.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Type parameter '{0}' of exported type alias has or is using private name '{1}'.", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "Conflicting definitions for '{0}' found at '{1}' and '{2}'. Consider installing a specific version of this library to resolve the conflict.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Parameter '{0}' of index signature from exported interface has or is using name '{1}' from private module '{2}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Parameter '{0}' of index signature from exported interface has or is using private name '{1}'.", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "Property '{0}' of exported class expression may not be private or protected.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Public static method '{0}' of exported class has or is using name '{1}' from external module {2} but cannot be named.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Public static method '{0}' of exported class has or is using name '{1}' from private module '{2}'.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Public static method '{0}' of exported class has or is using private name '{1}'.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Public method '{0}' of exported class has or is using name '{1}' from external module {2} but cannot be named.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Public method '{0}' of exported class has or is using name '{1}' from private module '{2}'.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Public method '{0}' of exported class has or is using private name '{1}'.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Method '{0}' of exported interface has or is using name '{1}' from private module '{2}'.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Method '{0}' of exported interface has or is using private name '{1}'.", "The_current_host_does_not_support_the_0_option_5001": "The current host does not support the '{0}' option.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Cannot find the common subdirectory path for the input files.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "File specification cannot end in a recursive directory wildcard ('**'): '{0}'.", "Cannot_read_file_0_Colon_1_5012": "Cannot read file '{0}': {1}.", "Failed_to_parse_file_0_Colon_1_5014": "Failed to parse file '{0}': {1}.", "Unknown_compiler_option_0_5023": "Unknown compiler option '{0}'.", "Compiler_option_0_requires_a_value_of_type_1_5024": "Compiler option '{0}' requires a value of type {1}.", "Could_not_write_file_0_Colon_1_5033": "Could not write file '{0}': {1}.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Option 'project' cannot be mixed with source files on a command line.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "Option 'isolatedModules' can only be used when either option '--module' is provided or option 'target' is 'ES2015' or higher.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "Option '{0} can only be used when either option '--inlineSourceMap' or option '--sourceMap' is provided.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Option '{0}' cannot be specified without specifying option '{1}'.", "Option_0_cannot_be_specified_with_option_1_5053": "Option '{0}' cannot be specified with option '{1}'.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "A 'tsconfig.json' file is already defined at: '{0}'.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Cannot write file '{0}' because it would overwrite input file.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Cannot write file '{0}' because it would be overwritten by multiple input files.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Cannot find a tsconfig.json file at the specified directory: '{0}'.", "The_specified_path_does_not_exist_Colon_0_5058": "The specified path does not exist: '{0}'.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Invalid value for '--reactNamespace'. '{0}' is not a valid identifier.", "Option_paths_cannot_be_used_without_specifying_baseUrl_option_5060": "Option 'paths' cannot be used without specifying '--baseUrl' option.", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "Pattern '{0}' can have at most one '*' character.", "Substitution_0_in_pattern_1_in_can_have_at_most_one_Asterisk_character_5062": "Substitution '{0}' in pattern '{1}' in can have at most one '*' character.", "Substitutions_for_pattern_0_should_be_an_array_5063": "Substitutions for pattern '{0}' should be an array.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Substitution '{0}' for pattern '{1}' has incorrect type, expected 'string', got '{2}'.", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "File specification cannot contain a parent directory ('..') that appears after a recursive directory wildcard ('**'): '{0}'.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Substitutions for pattern '{0}' shouldn't be an empty array.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Invalid value for 'jsxFactory'. '{0}' is not a valid identifier or qualified-name.", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Adding a tsconfig.json file will help organize projects that contain both TypeScript and JavaScript files. Learn more at https://aka.ms/tsconfig.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Option '{0}' cannot be specified without specifying option '{1}' or option '{2}'.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Generates a sourcemap for each corresponding '.d.ts' file.", "Concatenate_and_emit_output_to_single_file_6001": "Concatenate and emit output to single file.", "Generates_corresponding_d_ts_file_6002": "Generates corresponding '.d.ts' file.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6003": "Specify the location where debugger should locate map files instead of generated locations.", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Specify the location where debugger should locate TypeScript files instead of source locations.", "Watch_input_files_6005": "Watch input files.", "Redirect_output_structure_to_the_directory_6006": "Redirect output structure to the directory.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Do not erase const enum declarations in generated code.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Do not emit outputs if any errors were reported.", "Do_not_emit_comments_to_output_6009": "Do not emit comments to output.", "Do_not_emit_outputs_6010": "Do not emit outputs.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Allow default imports from modules with no default export. This does not affect code emit, just typechecking.", "Skip_type_checking_of_declaration_files_6012": "Skip type checking of declaration files.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Do not resolve the real path of symlinks.", "Only_emit_d_ts_declaration_files_6014": "Only emit '.d.ts' declaration files.", "Specify_ECMAScript_target_version_Colon_ES3_default_ES5_ES2015_ES2016_ES2017_ES2018_or_ESNEXT_6015": "Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017','ES2018' or 'ESNEXT'.", "Specify_module_code_generation_Colon_none_commonjs_amd_system_umd_es2015_or_ESNext_6016": "Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', or 'ESNext'.", "Print_this_message_6017": "Print this message.", "Print_the_compiler_s_version_6019": "Print the compiler's version.", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Compile the project given the path to its configuration file, or to a folder with a 'tsconfig.json'.", "Syntax_Colon_0_6023": "Syntax: {0}", "options_6024": "options", "file_6025": "file", "Examples_Colon_0_6026": "Examples: {0}", "Options_Colon_6027": "Options:", "Version_0_6029": "Version {0}", "Insert_command_line_options_and_files_from_a_file_6030": "Insert command line options and files from a file.", "Starting_compilation_in_watch_mode_6031": "Starting compilation in watch mode...", "File_change_detected_Starting_incremental_compilation_6032": "File change detected. Starting incremental compilation...", "KIND_6034": "KIND", "FILE_6035": "FILE", "VERSION_6036": "VERSION", "LOCATION_6037": "LOCATION", "DIRECTORY_6038": "DIRECTORY", "STRATEGY_6039": "STRATEGY", "FILE_OR_DIRECTORY_6040": "FILE OR DIRECTORY", "Generates_corresponding_map_file_6043": "Generates corresponding '.map' file.", "Compiler_option_0_expects_an_argument_6044": "Compiler option '{0}' expects an argument.", "Unterminated_quoted_string_in_response_file_0_6045": "Unterminated quoted string in response file '{0}'.", "Argument_for_0_option_must_be_Colon_1_6046": "Argument for '{0}' option must be: {1}.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Locale must be of the form <language> or <language>-<territory>. For example '{0}' or '{1}'.", "Unsupported_locale_0_6049": "Unsupported locale '{0}'.", "Unable_to_open_file_0_6050": "Unable to open file '{0}'.", "Corrupted_locale_file_0_6051": "Corrupted locale file {0}.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Raise error on expressions and declarations with an implied 'any' type.", "File_0_not_found_6053": "File '{0}' not found.", "File_0_has_unsupported_extension_The_only_supported_extensions_are_1_6054": "File '{0}' has unsupported extension. The only supported extensions are {1}.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Suppress noImplicitAny errors for indexing objects lacking index signatures.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Do not emit declarations for code that has an '@internal' annotation.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Specify the root directory of input files. Use to control the output directory structure with --outDir.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "File '{0}' is not under 'rootDir' '{1}'. 'rootDir' is expected to contain all source files.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Specify the end of line sequence to be used when emitting files: 'CRLF' (dos) or 'LF' (unix).", "NEWLINE_6061": "NEWLINE", "Option_0_can_only_be_specified_in_tsconfig_json_file_6064": "Option '{0}' can only be specified in 'tsconfig.json' file.", "Enables_experimental_support_for_ES7_decorators_6065": "Enables experimental support for ES7 decorators.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Enables experimental support for emitting type metadata for decorators.", "Enables_experimental_support_for_ES7_async_functions_6068": "Enables experimental support for ES7 async functions.", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "Specify module resolution strategy: 'node' (Node.js) or 'classic' (TypeScript pre-1.6).", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Initializes a TypeScript project and creates a tsconfig.json file.", "Successfully_created_a_tsconfig_json_file_6071": "Successfully created a tsconfig.json file.", "Suppress_excess_property_checks_for_object_literals_6072": "Suppress excess property checks for object literals.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Stylize errors and messages using color and context (experimental).", "Do_not_report_errors_on_unused_labels_6074": "Do not report errors on unused labels.", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Report error when not all code paths in function return a value.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Report errors for fallthrough cases in switch statement.", "Do_not_report_errors_on_unreachable_code_6077": "Do not report errors on unreachable code.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Disallow inconsistently-cased references to the same file.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Specify library files to be included in the compilation.", "Specify_JSX_code_generation_Colon_preserve_react_native_or_react_6080": "Specify JSX code generation: 'preserve', 'react-native', or 'react'.", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "File '{0}' has an unsupported extension, so skipping it.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Only 'amd' and 'system' modules are supported alongside --{0}.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Base directory to resolve non-absolute module names.", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Deprecated] Use '--jsxFactory' instead. Specify the object invoked for createElement when targeting 'react' JSX emit", "Enable_tracing_of_the_name_resolution_process_6085": "Enable tracing of the name resolution process.", "Resolving_module_0_from_1_6086": "======== Resolving module '{0}' from '{1}'. ========", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Explicitly specified module resolution kind: '{0}'.", "Module_resolution_kind_is_not_specified_using_0_6088": "Module resolution kind is not specified, using '{0}'.", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Module name '{0}' was successfully resolved to '{1}'. ========", "Module_name_0_was_not_resolved_6090": "======== Module name '{0}' was not resolved. ========", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "'paths' option is specified, looking for a pattern to match module name '{0}'.", "Module_name_0_matched_pattern_1_6092": "Module name '{0}', matched pattern '{1}'.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Trying substitution '{0}', candidate module location: '{1}'.", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Resolving module name '{0}' relative to base url '{1}' - '{2}'.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "Loading module as file / folder, candidate module location '{0}', target file type '{1}'.", "File_0_does_not_exist_6096": "File '{0}' does not exist.", "File_0_exist_use_it_as_a_name_resolution_result_6097": "File '{0}' exist - use it as a name resolution result.", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "Loading module '{0}' from 'node_modules' folder, target file type '{1}'.", "Found_package_json_at_0_6099": "Found 'package.json' at '{0}'.", "package_json_does_not_have_a_0_field_6100": "'package.json' does not have a '{0}' field.", "package_json_has_0_field_1_that_references_2_6101": "'package.json' has '{0}' field '{1}' that references '{2}'.", "Allow_javascript_files_to_be_compiled_6102": "Allow javascript files to be compiled.", "Option_0_should_have_array_of_strings_as_a_value_6103": "Option '{0}' should have array of strings as a value.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "Checking if '{0}' is the longest matching prefix for '{1}' - '{2}'.", "Expected_type_of_0_field_in_package_json_to_be_string_got_1_6105": "Expected type of '{0}' field in 'package.json' to be 'string', got '{1}'.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "'baseUrl' option is set to '{0}', using this value to resolve non-relative module name '{1}'.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "'rootDirs' option is set, using it to resolve relative module name '{0}'.", "Longest_matching_prefix_for_0_is_1_6108": "Longest matching prefix for '{0}' is '{1}'.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Loading '{0}' from the root dir '{1}', candidate location '{2}'.", "Trying_other_entries_in_rootDirs_6110": "Trying other entries in 'rootDirs'.", "Module_resolution_using_rootDirs_has_failed_6111": "Module resolution using 'rootDirs' has failed.", "Do_not_emit_use_strict_directives_in_module_output_6112": "Do not emit 'use strict' directives in module output.", "Enable_strict_null_checks_6113": "Enable strict null checks.", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Unknown option 'excludes'. Did you mean 'exclude'?", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Raise error on 'this' expressions with an implied 'any' type.", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Resolving type reference directive '{0}', containing file '{1}', root directory '{2}'. ========", "Resolving_using_primary_search_paths_6117": "Resolving using primary search paths...", "Resolving_from_node_modules_folder_6118": "Resolving from node_modules folder...", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== Type reference directive '{0}' was successfully resolved to '{1}', primary: {2}. ========", "Type_reference_directive_0_was_not_resolved_6120": "======== Type reference directive '{0}' was not resolved. ========", "Resolving_with_primary_search_path_0_6121": "Resolving with primary search path '{0}'.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Root directory cannot be determined, skipping primary search paths.", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Resolving type reference directive '{0}', containing file '{1}', root directory not set. ========", "Type_declaration_files_to_be_included_in_compilation_6124": "Type declaration files to be included in compilation.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Looking up in 'node_modules' folder, initial location '{0}'.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Containing file is not specified and root directory cannot be determined, skipping lookup in 'node_modules' folder.", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Resolving type reference directive '{0}', containing file not set, root directory '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Resolving type reference directive '{0}', containing file not set, root directory not set. ========", "Resolving_real_path_for_0_result_1_6130": "Resolving real path for '{0}', result '{1}'.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Cannot compile modules using option '{0}' unless the '--module' flag is 'amd' or 'system'.", "File_name_0_has_a_1_extension_stripping_it_6132": "File name '{0}' has a '{1}' extension - stripping it.", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}' is declared but its value is never read.", "Report_errors_on_unused_locals_6134": "Report errors on unused locals.", "Report_errors_on_unused_parameters_6135": "Report errors on unused parameters.", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "The maximum dependency depth to search under node_modules and load JavaScript files.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Cannot import type declaration files. Consider importing '{0}' instead of '{1}'.", "Property_0_is_declared_but_its_value_is_never_read_6138": "Property '{0}' is declared but its value is never read.", "Import_emit_helpers_from_tslib_6139": "Import emit helpers from 'tslib'.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "Auto discovery for typings is enabled in project '{0}'. Running extra resolution pass for module '{1}' using cache location '{2}'.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Parse in strict mode and emit \"use strict\" for each source file.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "Module '{0}' was resolved to '{1}', but '--jsx' is not set.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Module '{0}' was resolved as locally declared ambient module in file '{1}'.", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "Module '{0}' was resolved as ambient module declared in '{1}' since this file was not modified.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Specify the JSX factory function to use when targeting 'react' JSX emit, e.g. 'React.createElement' or 'h'.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "Resolution for module '{0}' was found in cache from location '{1}'.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "Directory '{0}' does not exist, skipping all lookups in it.", "Show_diagnostic_information_6149": "Show diagnostic information.", "Show_verbose_diagnostic_information_6150": "Show verbose diagnostic information.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Emit a single file with source maps instead of having a separate file.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Emit the source alongside the sourcemaps within a single file; requires '--inlineSourceMap' or '--sourceMap' to be set.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transpile each file as a separate module (similar to 'ts.transpileModule').", "Print_names_of_generated_files_part_of_the_compilation_6154": "Print names of generated files part of the compilation.", "Print_names_of_files_part_of_the_compilation_6155": "Print names of files part of the compilation.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "The locale used when displaying messages to the user (e.g. 'en-us')", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Do not generate custom helper functions like '__extends' in compiled output.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Do not include the default library file (lib.d.ts).", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Do not add triple-slash references or imported modules to the list of compiled files.", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Deprecated] Use '--skipLibCheck' instead. Skip type checking of default library declaration files.", "List_of_folders_to_include_type_definitions_from_6161": "List of folders to include type definitions from.", "Disable_size_limitations_on_JavaScript_projects_6162": "Disable size limitations on JavaScript projects.", "The_character_set_of_the_input_files_6163": "The character set of the input files.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6164": "Emit a UTF-8 Byte Order Mark (BOM) in the beginning of output files.", "Do_not_truncate_error_messages_6165": "Do not truncate error messages.", "Output_directory_for_generated_declaration_files_6166": "Output directory for generated declaration files.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "A series of entries which re-map imports to lookup locations relative to the 'baseUrl'.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "List of root folders whose combined content represents the structure of the project at runtime.", "Show_all_compiler_options_6169": "Show all compiler options.", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Deprecated] Use '--outFile' instead. Concatenate and emit output to single file", "Command_line_Options_6171": "Command-line Options", "Basic_Options_6172": "Basic Options", "Strict_Type_Checking_Options_6173": "Strict Type-Checking Options", "Module_Resolution_Options_6174": "Module Resolution Options", "Source_Map_Options_6175": "Source Map Options", "Additional_Checks_6176": "Additional Checks", "Experimental_Options_6177": "Experimental Options", "Advanced_Options_6178": "Advanced Options", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "Provide full support for iterables in 'for-of', spread, and destructuring when targeting 'ES5' or 'ES3'.", "Enable_all_strict_type_checking_options_6180": "Enable all strict type-checking options.", "List_of_language_service_plugins_6181": "List of language service plugins.", "Scoped_package_detected_looking_in_0_6182": "Scoped package detected, looking in '{0}'", "Reusing_resolution_of_module_0_to_file_1_from_old_program_6183": "Reusing resolution of module '{0}' to file '{1}' from old program.", "Reusing_module_resolutions_originating_in_0_since_resolutions_are_unchanged_from_old_program_6184": "Reusing module resolutions originating in '{0}' since resolutions are unchanged from old program.", "Disable_strict_checking_of_generic_signatures_in_function_types_6185": "Disable strict checking of generic signatures in function types.", "Enable_strict_checking_of_function_types_6186": "Enable strict checking of function types.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Enable strict checking of property initialization in classes.", "Numeric_separators_are_not_allowed_here_6188": "Numeric separators are not allowed here.", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Multiple consecutive numeric separators are not permitted.", "Found_package_json_at_0_Package_ID_is_1_6190": "Found 'package.json' at '{0}'. Package ID is '{1}'.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Whether to keep outdated console output in watch mode instead of clearing the screen.", "All_imports_in_import_declaration_are_unused_6192": "All imports in import declaration are unused.", "Found_1_error_Watching_for_file_changes_6193": "Found 1 error. Watching for file changes.", "Found_0_errors_Watching_for_file_changes_6194": "Found {0} errors. Watching for file changes.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "Resolve 'keyof' to string valued property names only (no numbers or symbols).", "_0_is_declared_but_never_used_6196": "'{0}' is declared but never used.", "Variable_0_implicitly_has_an_1_type_7005": "Variable '{0}' implicitly has an '{1}' type.", "Parameter_0_implicitly_has_an_1_type_7006": "Parameter '{0}' implicitly has an '{1}' type.", "Member_0_implicitly_has_an_1_type_7008": "Member '{0}' implicitly has an '{1}' type.", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "'new' expression, whose target lacks a construct signature, implicitly has an 'any' type.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}', which lacks return-type annotation, implicitly has an '{1}' return type.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "Function expression, which lacks return-type annotation, implicitly has an '{0}' return type.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "Construct signature, which lacks return-type annotation, implicitly has an 'any' return type.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "Element implicitly has an 'any' type because index expression is not of type 'number'.", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Could not find a declaration file for module '{0}'. '{1}' implicitly has an 'any' type.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "Element implicitly has an 'any' type because type '{0}' has no index signature.", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "Object literal's property '{0}' implicitly has an '{1}' type.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Rest parameter '{0}' implicitly has an 'any[]' type.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "Call signature, which lacks return-type annotation, implicitly has an 'any' return type.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "Function implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "Generator_implicitly_has_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_return_typ_7025": "Generator implicitly has type '{0}' because it does not yield any values. Consider supplying a return type.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "JSX element implicitly has type 'any' because no interface 'JSX.{0}' exists.", "Unreachable_code_detected_7027": "Unreachable code detected.", "Unused_label_7028": "Unused label.", "Fallthrough_case_in_switch_7029": "Fallthrough case in switch.", "Not_all_code_paths_return_a_value_7030": "Not all code paths return a value.", "Binding_element_0_implicitly_has_an_1_type_7031": "Binding element '{0}' implicitly has an '{1}' type.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "Property '{0}' implicitly has type 'any', because its set accessor lacks a parameter type annotation.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "Property '{0}' implicitly has type 'any', because its get accessor lacks a return type annotation.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "Variable '{0}' implicitly has type '{1}' in some locations where its type cannot be determined.", "Try_npm_install_types_Slash_0_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare_mod_7035": "Try `npm install @types/{0}` if it exists or add a new declaration (.d.ts) file containing `declare module '{0}';`", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "Dynamic import's specifier must be of type 'string', but here has type '{0}'.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'.", "A_namespace_style_import_cannot_be_called_or_constructed_and_will_cause_a_failure_at_runtime_7038": "A namespace-style import cannot be called or constructed, and will cause a failure at runtime.", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Mapped object type implicitly has an 'any' template type.", "You_cannot_rename_this_element_8000": "You cannot rename this element.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "You cannot rename elements that are defined in the standard TypeScript library.", "import_can_only_be_used_in_a_ts_file_8002": "'import ... =' can only be used in a .ts file.", "export_can_only_be_used_in_a_ts_file_8003": "'export=' can only be used in a .ts file.", "type_parameter_declarations_can_only_be_used_in_a_ts_file_8004": "'type parameter declarations' can only be used in a .ts file.", "implements_clauses_can_only_be_used_in_a_ts_file_8005": "'implements clauses' can only be used in a .ts file.", "interface_declarations_can_only_be_used_in_a_ts_file_8006": "'interface declarations' can only be used in a .ts file.", "module_declarations_can_only_be_used_in_a_ts_file_8007": "'module declarations' can only be used in a .ts file.", "type_aliases_can_only_be_used_in_a_ts_file_8008": "'type aliases' can only be used in a .ts file.", "_0_can_only_be_used_in_a_ts_file_8009": "'{0}' can only be used in a .ts file.", "types_can_only_be_used_in_a_ts_file_8010": "'types' can only be used in a .ts file.", "type_arguments_can_only_be_used_in_a_ts_file_8011": "'type arguments' can only be used in a .ts file.", "parameter_modifiers_can_only_be_used_in_a_ts_file_8012": "'parameter modifiers' can only be used in a .ts file.", "non_null_assertions_can_only_be_used_in_a_ts_file_8013": "'non-null assertions' can only be used in a .ts file.", "enum_declarations_can_only_be_used_in_a_ts_file_8015": "'enum declarations' can only be used in a .ts file.", "type_assertion_expressions_can_only_be_used_in_a_ts_file_8016": "'type assertion expressions' can only be used in a .ts file.", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "Octal literal types must use ES2015 syntax. Use the syntax '{0}'.", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "Octal literals are not allowed in enums members initializer. Use the syntax '{0}'.", "Report_errors_in_js_files_8019": "Report errors in .js files.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc types can only be used inside documentation comments.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc '@typedef' tag should either have a type annotation or be followed by '@property' or '@member' tags.", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc '@{0}' is not attached to a class.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSDoc '@{0} {1}' does not match the 'extends {2}' clause.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc '@param' tag has name '{0}', but there is no parameter with that name.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Class declarations cannot have more than one `@augments` or `@extends` tag.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Expected {0} type arguments; provide these with an '@extends' tag.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Expected {0}-{1} type arguments; provide these with an '@extends' tag.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' may only appear in the last parameter of a signature.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc '@param' tag has name '{0}', but there is no parameter with that name. It would match 'arguments' if it had an array type.", "Only_identifiers_Slashqualified_names_with_optional_type_arguments_are_currently_supported_in_a_clas_9002": "Only identifiers/qualified-names with optional type arguments are currently supported in a class 'extends' clause.", "class_expressions_are_not_currently_supported_9003": "'class' expressions are not currently supported.", "Language_service_is_disabled_9004": "Language service is disabled.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "JSX attributes must only be assigned a non-empty 'expression'.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX elements cannot have multiple attributes with the same name.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Expected corresponding JSX closing tag for '{0}'.", "JSX_attribute_expected_17003": "JSX attribute expected.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Cannot use JSX unless the '--jsx' flag is provided.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "A constructor cannot contain a 'super' call when its class extends 'null'.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "An unary expression with the '{0}' operator is not allowed in the left-hand side of an exponentiation expression. Consider enclosing the expression in parentheses.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "A type assertion expression is not allowed in the left-hand side of an exponentiation expression. Consider enclosing the expression in parentheses.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "JSX element '{0}' has no corresponding closing tag.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "'super' must be called before accessing 'this' in the constructor of a derived class.", "Unknown_type_acquisition_option_0_17010": "Unknown type acquisition option '{0}'.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "'super' must be called before accessing a property of 'super' in the constructor of a derived class.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' is not a valid meta-property for keyword '{1}'. Did you mean '{2}'?", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "Meta-property '{0}' is only allowed in the body of a function declaration, function expression, or constructor.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX fragment has no corresponding closing tag.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Expected corresponding closing tag for JSX fragment.", "JSX_fragment_is_not_supported_when_using_jsxFactory_17016": "JSX fragment is not supported when using --jsxFactory", "JSX_fragment_is_not_supported_when_using_an_inline_JSX_factory_pragma_17017": "JSX fragment is not supported when using an inline JSX factory pragma", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Circularity detected while resolving configuration: {0}", "A_path_in_an_extends_option_must_be_relative_or_rooted_but_0_is_not_18001": "A path in an 'extends' option must be relative or rooted, but '{0}' is not.", "The_files_list_in_config_file_0_is_empty_18002": "The 'files' list in config file '{0}' is empty.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "No inputs were found in config file '{0}'. Specified 'include' paths were '{1}' and 'exclude' paths were '{2}'.", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES6_module_80001": "File is a CommonJS module; it may be converted to an ES6 module.", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "This constructor function may be converted to a class declaration.", "Import_may_be_converted_to_a_default_import_80003": "Import may be converted to a default import.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc types may be moved to TypeScript types.", "require_call_may_be_converted_to_an_import_80005": "'require' call may be converted to an import.", "Add_missing_super_call_90001": "Add missing 'super()' call", "Make_super_call_the_first_statement_in_the_constructor_90002": "Make 'super()' call the first statement in the constructor", "Change_extends_to_implements_90003": "Change 'extends' to 'implements'", "Remove_declaration_for_Colon_0_90004": "Remove declaration for: '{0}'", "Remove_import_from_0_90005": "Remove import from '{0}'", "Implement_interface_0_90006": "Implement interface '{0}'", "Implement_inherited_abstract_class_90007": "Implement inherited abstract class", "Add_0_to_unresolved_variable_90008": "Add '{0}.' to unresolved variable", "Import_0_from_module_1_90013": "Import '{0}' from module \"{1}\"", "Change_0_to_1_90014": "Change '{0}' to '{1}'", "Add_0_to_existing_import_declaration_from_1_90015": "Add '{0}' to existing import declaration from \"{1}\"", "Declare_property_0_90016": "Declare property '{0}'", "Add_index_signature_for_property_0_90017": "Add index signature for property '{0}'", "Disable_checking_for_this_file_90018": "Disable checking for this file", "Ignore_this_error_message_90019": "Ignore this error message", "Initialize_property_0_in_the_constructor_90020": "Initialize property '{0}' in the constructor", "Initialize_static_property_0_90021": "Initialize static property '{0}'", "Change_spelling_to_0_90022": "Change spelling to '{0}'", "Declare_method_0_90023": "Declare method '{0}'", "Declare_static_method_0_90024": "Declare static method '{0}'", "Prefix_0_with_an_underscore_90025": "Prefix '{0}' with an underscore", "Rewrite_as_the_indexed_access_type_0_90026": "Rewrite as the indexed access type '{0}'", "Declare_static_property_0_90027": "Declare static property '{0}'", "Call_decorator_expression_90028": "Call decorator expression", "Add_async_modifier_to_containing_function_90029": "Add async modifier to containing function", "Convert_function_to_an_ES2015_class_95001": "Convert function to an ES2015 class", "Convert_function_0_to_class_95002": "Convert function '{0}' to class", "Extract_to_0_in_1_95004": "Extract to {0} in {1}", "Extract_function_95005": "Extract function", "Extract_constant_95006": "Extract constant", "Extract_to_0_in_enclosing_scope_95007": "Extract to {0} in enclosing scope", "Extract_to_0_in_1_scope_95008": "Extract to {0} in {1} scope", "Annotate_with_type_from_JSDoc_95009": "Annotate with type from JSDoc", "Annotate_with_types_from_JSDoc_95010": "Annotate with types from JSDoc", "Infer_type_of_0_from_usage_95011": "Infer type of '{0}' from usage", "Infer_parameter_types_from_usage_95012": "Infer parameter types from usage", "Convert_to_default_import_95013": "Convert to default import", "Install_0_95014": "Install '{0}'", "Replace_import_with_0_95015": "Replace import with '{0}'.", "Use_synthetic_default_member_95016": "Use synthetic 'default' member.", "Convert_to_ES6_module_95017": "Convert to ES6 module", "Add_undefined_type_to_property_0_95018": "Add 'undefined' type to property '{0}'", "Add_initializer_to_property_0_95019": "Add initializer to property '{0}'", "Add_definite_assignment_assertion_to_property_0_95020": "Add definite assignment assertion to property '{0}'", "Add_all_missing_members_95022": "Add all missing members", "Infer_all_types_from_usage_95023": "Infer all types from usage", "Delete_all_unused_declarations_95024": "Delete all unused declarations", "Prefix_all_unused_declarations_with_where_possible_95025": "Prefix all unused declarations with '_' where possible", "Fix_all_detected_spelling_errors_95026": "Fix all detected spelling errors", "Add_initializers_to_all_uninitialized_properties_95027": "Add initializers to all uninitialized properties", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Add definite assignment assertions to all uninitialized properties", "Add_undefined_type_to_all_uninitialized_properties_95029": "Add undefined type to all uninitialized properties", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Change all jsdoc-style types to TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Change all jsdoc-style types to TypeScript (and add '| undefined' to nullable types)", "Implement_all_unimplemented_interfaces_95032": "Implement all unimplemented interfaces", "Install_all_missing_types_packages_95033": "Install all missing types packages", "Rewrite_all_as_indexed_access_types_95034": "Rewrite all as indexed access types", "Convert_all_to_default_imports_95035": "Convert all to default imports", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Make all 'super()' calls the first statement in their constructor", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Add qualifier to all unresolved variables matching a member name", "Change_all_extended_interfaces_to_implements_95038": "Change all extended interfaces to 'implements'", "Add_all_missing_super_calls_95039": "Add all missing super calls", "Implement_all_inherited_abstract_classes_95040": "Implement all inherited abstract classes", "Add_all_missing_async_modifiers_95041": "Add all missing 'async' modifiers", "Add_ts_ignore_to_all_error_messages_95042": "Add '@ts-ignore' to all error messages", "Annotate_everything_with_types_from_JSDoc_95043": "Annotate everything with types from JSDoc", "Add_to_all_uncalled_decorators_95044": "Add '()' to all uncalled decorators", "Convert_all_constructor_functions_to_classes_95045": "Convert all constructor functions to classes", "Generate_get_and_set_accessors_95046": "Generate 'get' and 'set' accessors", "Convert_require_to_import_95047": "Convert 'require' to 'import'", "Convert_all_require_to_import_95048": "Convert all 'require' to 'import'"}