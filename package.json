{"name": "accounting-system", "version": "1.0.0", "description": "نظام محاسبي شامل", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.0", "sqlite3": "^5.1.6", "electron-store": "^8.1.0", "date-fns": "^2.29.3", "recharts": "^2.5.0"}, "devDependencies": {"electron": "^22.0.0", "electron-builder": "^24.0.0", "concurrently": "^7.6.0", "wait-on": "^7.0.1"}, "build": {"appId": "com.accounting.system", "productName": "نظام المحاسبة", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}