{"name": "accounting-system", "version": "1.0.0", "description": "نظام محاسبي شامل", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build"}, "dependencies": {"date-fns": "^2.30.0", "electron-is-dev": "^3.0.1", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "recharts": "^2.5.0", "sqlite3": "^5.1.7"}, "devDependencies": {"concurrently": "^7.6.0", "electron": "^22.3.27", "electron-builder": "^24.13.3", "wait-on": "^7.2.0"}, "build": {"appId": "com.accounting.system", "productName": "نظام المحاسبة", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}