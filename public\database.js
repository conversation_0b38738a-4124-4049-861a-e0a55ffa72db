const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

let db;

function initDatabase() {
  const dbPath = path.join(app.getPath('userData'), 'accounting.db');
  
  db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('خطأ في فتح قاعدة البيانات:', err.message);
    } else {
      console.log('تم الاتصال بقاعدة البيانات بنجاح');
      createTables();
    }
  });
}

function createTables() {
  // جدول كشف الحساب
  db.run(`CREATE TABLE IF NOT EXISTS account_statements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    description TEXT NOT NULL,
    debit REAL DEFAULT 0,
    credit REAL DEFAULT 0,
    balance REAL DEFAULT 0,
    account_type TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مشتريات المخزن
  db.run(`CREATE TABLE IF NOT EXISTS warehouse_purchases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    item_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL NOT NULL,
    total_price REAL NOT NULL,
    supplier TEXT,
    invoice_number TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مصروفات الحركة والنقل
  db.run(`CREATE TABLE IF NOT EXISTS transport_expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    transport_type TEXT NOT NULL,
    destination TEXT,
    amount REAL NOT NULL,
    driver_name TEXT,
    vehicle_number TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مصروفات المعيشة
  db.run(`CREATE TABLE IF NOT EXISTS living_expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    expense_type TEXT NOT NULL,
    amount REAL NOT NULL,
    beneficiary TEXT,
    description TEXT,
    receipt_number TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مصروفات الطلاء
  db.run(`CREATE TABLE IF NOT EXISTS paint_expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    paint_type TEXT NOT NULL,
    quantity REAL NOT NULL,
    unit_price REAL NOT NULL,
    total_amount REAL NOT NULL,
    supplier TEXT,
    project_name TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مصروفات المصنع
  db.run(`CREATE TABLE IF NOT EXISTS factory_expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    expense_category TEXT NOT NULL,
    description TEXT NOT NULL,
    amount REAL NOT NULL,
    department TEXT,
    approved_by TEXT,
    receipt_number TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول مسحوبات الموظفين
  db.run(`CREATE TABLE IF NOT EXISTS employee_withdrawals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    employee_id TEXT,
    withdrawal_type TEXT NOT NULL,
    amount REAL NOT NULL,
    reason TEXT,
    approved_by TEXT,
    repayment_date TEXT,
    status TEXT DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول إيداعات الخزينة
  db.run(`CREATE TABLE IF NOT EXISTS treasury_deposits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date TEXT NOT NULL,
    deposit_type TEXT NOT NULL,
    amount REAL NOT NULL,
    source TEXT NOT NULL,
    reference_number TEXT,
    deposited_by TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // جدول الإعدادات
  db.run(`CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  console.log('تم إنشاء جداول قاعدة البيانات بنجاح');
}

function getDatabase() {
  return db;
}

module.exports = {
  initDatabase,
  getDatabase
};
