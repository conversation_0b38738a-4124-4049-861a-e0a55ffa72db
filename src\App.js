import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import AccountStatements from './components/AccountStatements';
import WarehousePurchases from './components/WarehousePurchases';
import TransportExpenses from './components/TransportExpenses';
import LivingExpenses from './components/LivingExpenses';
import PaintExpenses from './components/PaintExpenses';
import FactoryExpenses from './components/FactoryExpenses';
import EmployeeWithdrawals from './components/EmployeeWithdrawals';
import TreasuryDeposits from './components/TreasuryDeposits';
import Reports from './components/Reports';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app">
        <Sidebar />
        <div className="main-content">
          <Header />
          <div className="content">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/account-statements" element={<AccountStatements />} />
              <Route path="/warehouse-purchases" element={<WarehousePurchases />} />
              <Route path="/transport-expenses" element={<TransportExpenses />} />
              <Route path="/living-expenses" element={<LivingExpenses />} />
              <Route path="/paint-expenses" element={<PaintExpenses />} />
              <Route path="/factory-expenses" element={<FactoryExpenses />} />
              <Route path="/employee-withdrawals" element={<EmployeeWithdrawals />} />
              <Route path="/treasury-deposits" element={<TreasuryDeposits />} />
              <Route path="/reports" element={<Reports />} />
            </Routes>
          </div>
        </div>
      </div>
    </Router>
  );
}

export default App;
