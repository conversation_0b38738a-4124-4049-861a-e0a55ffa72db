import React, { useState, useEffect } from 'react';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalIncome: 0,
    totalExpenses: 0,
    netProfit: 0,
    pendingWithdrawals: 0,
    warehouseValue: 0,
    treasuryBalance: 0
  });

  const [recentTransactions, setRecentTransactions] = useState([]);

  useEffect(() => {
    // هنا سيتم جلب البيانات من قاعدة البيانات
    // مؤقتاً سنستخدم بيانات تجريبية
    setStats({
      totalIncome: 150000,
      totalExpenses: 95000,
      netProfit: 55000,
      pendingWithdrawals: 12000,
      warehouseValue: 85000,
      treasuryBalance: 67000
    });

    setRecentTransactions([
      { id: 1, type: 'إيداع', description: 'إيداع نقدي', amount: 25000, date: '2024-01-15' },
      { id: 2, type: 'مصروف', description: 'مشتريات مخزن', amount: -8500, date: '2024-01-14' },
      { id: 3, type: 'مصروف', description: 'مصروفات نقل', amount: -3200, date: '2024-01-13' },
      { id: 4, type: 'سحب', description: 'سحب موظف', amount: -5000, date: '2024-01-12' },
      { id: 5, type: 'إيداع', description: 'مبيعات', amount: 18000, date: '2024-01-11' }
    ]);
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="dashboard">
      {/* إحصائيات سريعة */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#28a745' }}>
            {formatCurrency(stats.totalIncome)}
          </div>
          <div className="stat-label">إجمالي الإيرادات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(stats.totalExpenses)}
          </div>
          <div className="stat-label">إجمالي المصروفات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#007bff' }}>
            {formatCurrency(stats.netProfit)}
          </div>
          <div className="stat-label">صافي الربح</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#ffc107' }}>
            {formatCurrency(stats.pendingWithdrawals)}
          </div>
          <div className="stat-label">مسحوبات معلقة</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#6f42c1' }}>
            {formatCurrency(stats.warehouseValue)}
          </div>
          <div className="stat-label">قيمة المخزن</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#20c997' }}>
            {formatCurrency(stats.treasuryBalance)}
          </div>
          <div className="stat-label">رصيد الخزينة</div>
        </div>
      </div>

      {/* آخر المعاملات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">آخر المعاملات</h3>
          <button className="btn btn-secondary">عرض الكل</button>
        </div>
        
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>النوع</th>
                <th>الوصف</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
              </tr>
            </thead>
            <tbody>
              {recentTransactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>
                    <span className={`badge ${transaction.type === 'إيداع' ? 'badge-success' : 
                                              transaction.type === 'مصروف' ? 'badge-danger' : 'badge-warning'}`}>
                      {transaction.type}
                    </span>
                  </td>
                  <td>{transaction.description}</td>
                  <td style={{ 
                    color: transaction.amount > 0 ? '#28a745' : '#dc3545',
                    fontWeight: 'bold'
                  }}>
                    {formatCurrency(Math.abs(transaction.amount))}
                  </td>
                  <td>{new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* روابط سريعة */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">إجراءات سريعة</h3>
        </div>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <button className="btn btn-primary" style={{ padding: '15px', fontSize: '16px' }}>
            📋 إضافة معاملة جديدة
          </button>
          <button className="btn btn-primary" style={{ padding: '15px', fontSize: '16px' }}>
            📦 تسجيل مشتريات
          </button>
          <button className="btn btn-primary" style={{ padding: '15px', fontSize: '16px' }}>
            💰 إيداع خزينة
          </button>
          <button className="btn btn-primary" style={{ padding: '15px', fontSize: '16px' }}>
            📈 عرض التقارير
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
