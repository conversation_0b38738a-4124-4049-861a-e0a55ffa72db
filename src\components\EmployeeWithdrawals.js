import React, { useState } from 'react';

const EmployeeWithdrawals = () => {
  const [withdrawals, setWithdrawals] = useState([
    {
      id: 1,
      date: '2024-01-15',
      employeeName: 'محمد أحمد',
      employeeId: 'EMP-001',
      withdrawalType: 'سلفة',
      amount: 5000,
      reason: 'ظروف طارئة',
      approvedBy: 'مدير الموارد البشرية',
      repaymentDate: '2024-02-15',
      status: 'معلق'
    }
  ]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'معلق': return '#ffc107';
      case 'مسدد': return '#28a745';
      case 'متأخر': return '#dc3545';
      default: return '#6c757d';
    }
  };

  return (
    <div className="employee-withdrawals">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مسحوبات الموظفين</h3>
          <button className="btn btn-primary">
            ➕ إضافة مسحوبات جديدة
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>اسم الموظف</th>
                <th>رقم الموظف</th>
                <th>نوع المسحوبات</th>
                <th>المبلغ</th>
                <th>السبب</th>
                <th>تاريخ السداد</th>
                <th>الحالة</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {withdrawals.map((withdrawal) => (
                <tr key={withdrawal.id}>
                  <td>{new Date(withdrawal.date).toLocaleDateString('ar-SA')}</td>
                  <td>{withdrawal.employeeName}</td>
                  <td>{withdrawal.employeeId}</td>
                  <td>{withdrawal.withdrawalType}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(withdrawal.amount)}
                  </td>
                  <td>{withdrawal.reason}</td>
                  <td>{new Date(withdrawal.repaymentDate).toLocaleDateString('ar-SA')}</td>
                  <td>
                    <span 
                      style={{ 
                        padding: '4px 8px', 
                        borderRadius: '4px', 
                        backgroundColor: getStatusColor(withdrawal.status),
                        color: 'white',
                        fontSize: '12px'
                      }}
                    >
                      {withdrawal.status}
                    </span>
                  </td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EmployeeWithdrawals;
