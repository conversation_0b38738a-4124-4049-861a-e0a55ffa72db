import React, { useState } from 'react';

const FactoryExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      expenseCategory: 'صيانة',
      description: 'صيانة دورية للآلات',
      amount: 5500,
      department: 'الإنتاج',
      approvedBy: 'مدير المصنع',
      receiptNumber: 'FAC-001'
    }
  ]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="factory-expenses">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات المصنع</h3>
          <button className="btn btn-primary">
            ➕ إضافة مصروف مصنع
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>فئة المصروف</th>
                <th>الوصف</th>
                <th>المبلغ</th>
                <th>القسم</th>
                <th>معتمد من</th>
                <th>رقم الإيصال</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                  <td>{expense.expenseCategory}</td>
                  <td>{expense.description}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.amount)}
                  </td>
                  <td>{expense.department}</td>
                  <td>{expense.approvedBy}</td>
                  <td>{expense.receiptNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FactoryExpenses;
