import React from 'react';
import { useLocation } from 'react-router-dom';

const Header = () => {
  const location = useLocation();

  const getPageTitle = () => {
    const titles = {
      '/': 'لوحة التحكم',
      '/account-statements': 'كشف الحساب',
      '/warehouse-purchases': 'مشتريات المخزن',
      '/transport-expenses': 'مصروفات النقل',
      '/living-expenses': 'مصروفات المعيشة',
      '/paint-expenses': 'مصروفات الطلاء',
      '/factory-expenses': 'مصروفات المصنع',
      '/employee-withdrawals': 'مسحوبات الموظفين',
      '/treasury-deposits': 'إيداعات الخزينة',
      '/reports': 'التقارير'
    };
    return titles[location.pathname] || 'نظام المحاسبة';
  };

  const getCurrentDate = () => {
    const now = new Date();
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    };
    return now.toLocaleDateString('ar-SA', options);
  };

  return (
    <header className="header">
      <div>
        <h1>{getPageTitle()}</h1>
        <p style={{ fontSize: '14px', color: '#666', marginTop: '5px' }}>
          {getCurrentDate()}
        </p>
      </div>
      
      <div className="header-actions">
        <button className="btn btn-secondary">
          ⚙️ الإعدادات
        </button>
        <button className="btn btn-primary">
          💾 حفظ
        </button>
      </div>
    </header>
  );
};

export default Header;
