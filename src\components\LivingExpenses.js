import React, { useState } from 'react';

const LivingExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      expenseType: 'إقامة',
      amount: 2500,
      beneficiary: 'موظف المبيعات',
      description: 'إقامة فندقية لمدة 3 أيام',
      receiptNumber: 'REC-001'
    }
  ]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="living-expenses">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات المعيشة</h3>
          <button className="btn btn-primary">
            ➕ إضافة مصروف معيشة
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع المصروف</th>
                <th>المبلغ</th>
                <th>المستفيد</th>
                <th>الوصف</th>
                <th>رقم الإيصال</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                  <td>{expense.expenseType}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.amount)}
                  </td>
                  <td>{expense.beneficiary}</td>
                  <td>{expense.description}</td>
                  <td>{expense.receiptNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default LivingExpenses;
