import React, { useState } from 'react';

const PaintExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      paintType: 'دهان أكريليك',
      quantity: 50,
      unitPrice: 45,
      totalAmount: 2250,
      supplier: 'شركة الدهانات المتقدمة',
      projectName: 'مشروع المصنع الجديد',
      notes: 'جودة عالية مقاوم للرطوبة'
    }
  ]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  return (
    <div className="paint-expenses">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات الطلاء</h3>
          <button className="btn btn-primary">
            ➕ إضافة مصروف طلاء
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع الطلاء</th>
                <th>الكمية</th>
                <th>سعر الوحدة</th>
                <th>إجمالي المبلغ</th>
                <th>المورد</th>
                <th>اسم المشروع</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                  <td>{expense.paintType}</td>
                  <td>{expense.quantity}</td>
                  <td>{formatCurrency(expense.unitPrice)}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.totalAmount)}
                  </td>
                  <td>{expense.supplier}</td>
                  <td>{expense.projectName}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PaintExpenses;
