import React, { useState } from 'react';

const Reports = () => {
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  const [reportType, setReportType] = useState('شامل');

  const reportData = {
    income: 150000,
    expenses: {
      warehouse: 25000,
      transport: 8500,
      living: 5200,
      paint: 12000,
      factory: 18500,
      employees: 15000
    },
    deposits: 67000,
    withdrawals: 12000
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTotalExpenses = () => {
    return Object.values(reportData.expenses).reduce((sum, expense) => sum + expense, 0);
  };

  const getNetProfit = () => {
    return reportData.income - getTotalExpenses();
  };

  const generatePDF = () => {
    alert('سيتم إضافة وظيفة تصدير PDF قريباً');
  };

  return (
    <div className="reports">
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">التقارير المالية</h3>
          <div style={{ display: 'flex', gap: '10px' }}>
            <select 
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="form-input"
              style={{ width: 'auto' }}
            >
              <option value="2024-01">يناير 2024</option>
              <option value="2023-12">ديسمبر 2023</option>
              <option value="2023-11">نوفمبر 2023</option>
            </select>
            
            <select 
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="form-input"
              style={{ width: 'auto' }}
            >
              <option value="شامل">تقرير شامل</option>
              <option value="إيرادات">الإيرادات فقط</option>
              <option value="مصروفات">المصروفات فقط</option>
            </select>
            
            <button className="btn btn-primary" onClick={generatePDF}>
              📄 تصدير PDF
            </button>
          </div>
        </div>

        {/* ملخص مالي */}
        <div className="stats-grid" style={{ marginBottom: '30px' }}>
          <div className="stat-card">
            <div className="stat-value" style={{ color: '#28a745' }}>
              {formatCurrency(reportData.income)}
            </div>
            <div className="stat-label">إجمالي الإيرادات</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value" style={{ color: '#dc3545' }}>
              {formatCurrency(getTotalExpenses())}
            </div>
            <div className="stat-label">إجمالي المصروفات</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value" style={{ color: getNetProfit() >= 0 ? '#007bff' : '#dc3545' }}>
              {formatCurrency(getNetProfit())}
            </div>
            <div className="stat-label">صافي الربح/الخسارة</div>
          </div>
        </div>

        {/* تفصيل المصروفات */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
          <div className="card" style={{ margin: 0 }}>
            <h4 style={{ marginBottom: '15px' }}>تفصيل المصروفات</h4>
            <div className="table-container">
              <table className="table">
                <thead>
                  <tr>
                    <th>نوع المصروف</th>
                    <th>المبلغ</th>
                    <th>النسبة</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>مشتريات المخزن</td>
                    <td>{formatCurrency(reportData.expenses.warehouse)}</td>
                    <td>{((reportData.expenses.warehouse / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                  <tr>
                    <td>مصروفات النقل</td>
                    <td>{formatCurrency(reportData.expenses.transport)}</td>
                    <td>{((reportData.expenses.transport / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                  <tr>
                    <td>مصروفات المعيشة</td>
                    <td>{formatCurrency(reportData.expenses.living)}</td>
                    <td>{((reportData.expenses.living / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                  <tr>
                    <td>مصروفات الطلاء</td>
                    <td>{formatCurrency(reportData.expenses.paint)}</td>
                    <td>{((reportData.expenses.paint / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                  <tr>
                    <td>مصروفات المصنع</td>
                    <td>{formatCurrency(reportData.expenses.factory)}</td>
                    <td>{((reportData.expenses.factory / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                  <tr>
                    <td>مسحوبات الموظفين</td>
                    <td>{formatCurrency(reportData.expenses.employees)}</td>
                    <td>{((reportData.expenses.employees / getTotalExpenses()) * 100).toFixed(1)}%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div className="card" style={{ margin: 0 }}>
            <h4 style={{ marginBottom: '15px' }}>حركة الخزينة</h4>
            <div style={{ padding: '20px' }}>
              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>
                <span>إجمالي الإيداعات:</span>
                <strong style={{ color: '#28a745' }}>{formatCurrency(reportData.deposits)}</strong>
              </div>
              <div style={{ marginBottom: '15px', display: 'flex', justifyContent: 'space-between' }}>
                <span>إجمالي المسحوبات:</span>
                <strong style={{ color: '#dc3545' }}>{formatCurrency(reportData.withdrawals)}</strong>
              </div>
              <hr />
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '18px' }}>
                <span>رصيد الخزينة:</span>
                <strong style={{ color: '#007bff' }}>
                  {formatCurrency(reportData.deposits - reportData.withdrawals)}
                </strong>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
