import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/',
      label: 'لوحة التحكم',
      icon: '📊'
    },
    {
      path: '/account-statements',
      label: 'كشف الحساب',
      icon: '📋'
    },
    {
      path: '/warehouse-purchases',
      label: 'مشتريات المخزن',
      icon: '📦'
    },
    {
      path: '/transport-expenses',
      label: 'مصروفات النقل',
      icon: '🚛'
    },
    {
      path: '/living-expenses',
      label: 'مصروفات المعيشة',
      icon: '🏠'
    },
    {
      path: '/paint-expenses',
      label: 'مصروفات الطلاء',
      icon: '🎨'
    },
    {
      path: '/factory-expenses',
      label: 'مصروفات المصنع',
      icon: '🏭'
    },
    {
      path: '/employee-withdrawals',
      label: 'مسحوبات الموظفين',
      icon: '👥'
    },
    {
      path: '/treasury-deposits',
      label: 'إيداعات الخزينة',
      icon: '💰'
    },
    {
      path: '/reports',
      label: 'التقارير',
      icon: '📈'
    }
  ];

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2>نظام المحاسبة</h2>
        <p>الإصدار 1.0</p>
      </div>
      
      <nav className="sidebar-menu">
        {menuItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`menu-item ${location.pathname === item.path ? 'active' : ''}`}
          >
            <span className="menu-icon">{item.icon}</span>
            {item.label}
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
