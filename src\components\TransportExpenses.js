import React, { useState } from 'react';

const TransportExpenses = () => {
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      date: '2024-01-15',
      transportType: 'شحن بضائع',
      destination: 'الرياض',
      amount: 3200,
      driverName: 'أحمد محمد',
      vehicleNumber: 'أ ب ج 1234',
      notes: 'شحن سريع'
    }
  ]);

  const [showForm, setShowForm] = useState(false);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTotalExpenses = () => {
    return expenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  return (
    <div className="transport-expenses">
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#dc3545' }}>
            {formatCurrency(getTotalExpenses())}
          </div>
          <div className="stat-label">إجمالي مصروفات النقل</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#007bff' }}>
            {expenses.length}
          </div>
          <div className="stat-label">عدد الرحلات</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مصروفات الحركة والنقل</h3>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(!showForm)}
          >
            ➕ إضافة مصروف نقل
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع النقل</th>
                <th>الوجهة</th>
                <th>المبلغ</th>
                <th>اسم السائق</th>
                <th>رقم المركبة</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {expenses.map((expense) => (
                <tr key={expense.id}>
                  <td>{new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                  <td>{expense.transportType}</td>
                  <td>{expense.destination}</td>
                  <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                    {formatCurrency(expense.amount)}
                  </td>
                  <td>{expense.driverName}</td>
                  <td>{expense.vehicleNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TransportExpenses;
