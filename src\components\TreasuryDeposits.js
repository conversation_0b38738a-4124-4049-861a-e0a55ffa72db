import React, { useState } from 'react';

const TreasuryDeposits = () => {
  const [deposits, setDeposits] = useState([
    {
      id: 1,
      date: '2024-01-15',
      depositType: 'إيداع نقدي',
      amount: 25000,
      source: 'مبيعات يومية',
      referenceNumber: 'DEP-001',
      depositedBy: 'أمين الصندوق',
      notes: 'إيداع مبيعات اليوم'
    }
  ]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTotalDeposits = () => {
    return deposits.reduce((sum, deposit) => sum + deposit.amount, 0);
  };

  return (
    <div className="treasury-deposits">
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#28a745' }}>
            {formatCurrency(getTotalDeposits())}
          </div>
          <div className="stat-label">إجمالي الإيداعات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#007bff' }}>
            {deposits.length}
          </div>
          <div className="stat-label">عدد الإيداعات</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">إيداعات الخزينة</h3>
          <button className="btn btn-primary">
            ➕ إضافة إيداع جديد
          </button>
        </div>

        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>نوع الإيداع</th>
                <th>المبلغ</th>
                <th>المصدر</th>
                <th>رقم المرجع</th>
                <th>المودع</th>
                <th>ملاحظات</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {deposits.map((deposit) => (
                <tr key={deposit.id}>
                  <td>{new Date(deposit.date).toLocaleDateString('ar-SA')}</td>
                  <td>{deposit.depositType}</td>
                  <td style={{ fontWeight: 'bold', color: '#28a745' }}>
                    {formatCurrency(deposit.amount)}
                  </td>
                  <td>{deposit.source}</td>
                  <td>{deposit.referenceNumber}</td>
                  <td>{deposit.depositedBy}</td>
                  <td>{deposit.notes}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TreasuryDeposits;
