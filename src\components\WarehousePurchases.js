import React, { useState, useEffect } from 'react';

const WarehousePurchases = () => {
  const [purchases, setPurchases] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    itemName: '',
    quantity: '',
    unitPrice: '',
    supplier: '',
    invoiceNumber: '',
    notes: ''
  });

  useEffect(() => {
    // بيانات تجريبية
    setPurchases([
      {
        id: 1,
        date: '2024-01-15',
        itemName: 'مواد خام - حديد',
        quantity: 100,
        unitPrice: 50,
        totalPrice: 5000,
        supplier: 'شركة الحديد المتحدة',
        invoiceNumber: 'INV-001',
        notes: 'جودة عالية'
      },
      {
        id: 2,
        date: '2024-01-14',
        itemName: 'أدوات تصنيع',
        quantity: 25,
        unitPrice: 120,
        totalPrice: 3000,
        supplier: 'مؤسسة الأدوات',
        invoiceNumber: 'INV-002',
        notes: ''
      }
    ]);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const newPurchase = {
      id: purchases.length + 1,
      ...formData,
      quantity: parseInt(formData.quantity),
      unitPrice: parseFloat(formData.unitPrice),
      totalPrice: parseInt(formData.quantity) * parseFloat(formData.unitPrice)
    };

    setPurchases(prev => [newPurchase, ...prev]);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      itemName: '',
      quantity: '',
      unitPrice: '',
      supplier: '',
      invoiceNumber: '',
      notes: ''
    });
    setShowForm(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTotalValue = () => {
    return purchases.reduce((sum, purchase) => sum + purchase.totalPrice, 0);
  };

  const getTotalItems = () => {
    return purchases.reduce((sum, purchase) => sum + purchase.quantity, 0);
  };

  return (
    <div className="warehouse-purchases">
      {/* إحصائيات المخزن */}
      <div className="stats-grid" style={{ marginBottom: '30px' }}>
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#007bff' }}>
            {formatCurrency(getTotalValue())}
          </div>
          <div className="stat-label">إجمالي قيمة المشتريات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#28a745' }}>
            {getTotalItems()}
          </div>
          <div className="stat-label">إجمالي الكمية</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value" style={{ color: '#6f42c1' }}>
            {purchases.length}
          </div>
          <div className="stat-label">عدد المشتريات</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="card-title">مشتريات المخزن</h3>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(!showForm)}
          >
            ➕ إضافة مشتريات جديدة
          </button>
        </div>

        {/* نموذج إضافة مشتريات */}
        {showForm && (
          <form onSubmit={handleSubmit} style={{ marginBottom: '20px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="form-input"
                  required
                />
              </div>
              
              <div className="form-group">
                <label className="form-label">رقم الفاتورة</label>
                <input
                  type="text"
                  name="invoiceNumber"
                  value={formData.invoiceNumber}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="رقم الفاتورة"
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">اسم الصنف</label>
              <input
                type="text"
                name="itemName"
                value={formData.itemName}
                onChange={handleInputChange}
                className="form-input"
                placeholder="اسم الصنف أو المادة"
                required
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label className="form-label">الكمية</label>
                <input
                  type="number"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="الكمية"
                  required
                />
              </div>
              
              <div className="form-group">
                <label className="form-label">سعر الوحدة</label>
                <input
                  type="number"
                  name="unitPrice"
                  value={formData.unitPrice}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="سعر الوحدة"
                  step="0.01"
                  required
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">المورد</label>
              <input
                type="text"
                name="supplier"
                value={formData.supplier}
                onChange={handleInputChange}
                className="form-input"
                placeholder="اسم المورد"
              />
            </div>

            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="form-input"
                placeholder="ملاحظات إضافية"
                rows="3"
              />
            </div>

            {formData.quantity && formData.unitPrice && (
              <div className="alert alert-info">
                <strong>إجمالي المبلغ: {formatCurrency(formData.quantity * formData.unitPrice)}</strong>
              </div>
            )}

            <div style={{ display: 'flex', gap: '10px' }}>
              <button type="submit" className="btn btn-primary">
                💾 حفظ المشتريات
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => setShowForm(false)}
              >
                ❌ إلغاء
              </button>
            </div>
          </form>
        )}

        {/* جدول المشتريات */}
        <div className="table-container">
          <table className="table">
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>اسم الصنف</th>
                <th>الكمية</th>
                <th>سعر الوحدة</th>
                <th>إجمالي المبلغ</th>
                <th>المورد</th>
                <th>رقم الفاتورة</th>
                <th>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {purchases.map((purchase) => (
                <tr key={purchase.id}>
                  <td>{new Date(purchase.date).toLocaleDateString('ar-SA')}</td>
                  <td>{purchase.itemName}</td>
                  <td>{purchase.quantity}</td>
                  <td>{formatCurrency(purchase.unitPrice)}</td>
                  <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                    {formatCurrency(purchase.totalPrice)}
                  </td>
                  <td>{purchase.supplier}</td>
                  <td>{purchase.invoiceNumber}</td>
                  <td>
                    <button className="btn btn-secondary" style={{ padding: '5px 10px', fontSize: '12px' }}>
                      ✏️ تعديل
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WarehousePurchases;
